# LORE 推理调试指南

本文档提供了在原始LORE仓库中添加推理调试信息的详细指导，用于诊断推理效果问题。

## 问题诊断背景

当LORE推理结果不理想时（如检测分数过低、无有效检测等），需要在关键位置添加调试信息来定位问题。

## 调试信息添加位置

### 1. **在 `lib/detectors/base_detector.py` 的 `run` 方法中**
- **位置**: `process` 方法调用后，大约第247行附近
- **目的**: 检查backbone输出的数值范围
- **添加代码**:
```python
# 在 outputs, output, dets, corner_st_reg, forward_time, logi, cr, keep = self.process(...) 之后
print(f"[INFERENCE] Backbone outputs:")
print(f"[INFERENCE]   hm range: {output['hm'].min():.4f} - {output['hm'].max():.4f}")
print(f"[INFERENCE]   wh range: {output['wh'].min():.4f} - {output['wh'].max():.4f}")
print(f"[INFERENCE]   reg range: {output['reg'].min():.4f} - {output['reg'].max():.4f}")
```

### 2. **在 `lib/detectors/ctdet.py` 的 `process` 方法中**
- **位置**: 模型forward调用后，大约第46行附近
- **目的**: 检查原始backbone输出
- **添加代码**:
```python
# 在 outputs = self.model(images) 之后
if 'hm' in outputs[-1]:
    hm = outputs[-1]['hm']
    print(f"[INFERENCE] Raw hm range: {hm.min():.4f} - {hm.max():.4f}")
    wh = outputs[-1]['wh']
    print(f"[INFERENCE] Raw wh range: {wh.min():.4f} - {wh.max():.4f}")
```

### 3. **在 `lib/models/decode.py` 的 `ctdet_4ps_decode` 函数中**
- **位置**: 函数开始处，大约第85行附近
- **目的**: 分析sigmoid后的heatmap质量
- **添加代码**:
```python
# 在 heat = _sigmoid(heat) 之后
print(f"[INFERENCE] Heat after sigmoid - shape: {heat.shape}, min/max: {heat.min():.4f}/{heat.max():.4f}")
activations_01 = (heat > 0.1).sum().item()
activations_02 = (heat > 0.2).sum().item()
print(f"[INFERENCE] Activations > 0.1: {activations_01}")
print(f"[INFERENCE] Activations > 0.2: {activations_02}")
```

### 4. **在 `lib/models/decode.py` 的 `_topk` 函数中**
- **位置**: 函数末尾，返回前
- **目的**: 分析top-K检测分数
- **添加代码**:
```python
# 在 return 语句前
print(f"[INFERENCE] Top-K results:")
print(f"[INFERENCE]   Best scores: {topk_score[0, :min(10, topk_score.shape[1])].cpu().numpy()}")
print(f"[INFERENCE]   Score range: {topk_score.min():.4f} - {topk_score.max():.4f}")
valid_count = (topk_score > 0.1).sum().item()
print(f"[INFERENCE]   Scores > 0.1: {valid_count}")
```

### 5. **在 `lib/models/decode.py` 的 `ctdet_4ps_decode` 函数末尾**
- **位置**: 返回detections前，大约第130行附近
- **目的**: 分析最终检测结果质量
- **添加代码**:
```python
# 在 return detections 前
if len(detections) > 0:
    scores = detections[0][:, 8] if len(detections) > 0 else []
    if len(scores) > 0:
        thresholds = [0.05, 0.1, 0.2, 0.3]
        for thresh in thresholds:
            count = (scores > thresh).sum()
            print(f"[INFERENCE] Final detections > {thresh}: {count}")
        
        valid_scores = scores[scores > 0.05]
        if len(valid_scores) > 0:
            print(f"[INFERENCE] Valid detection scores: {valid_scores[:5]}")
            print(f"[INFERENCE] Score statistics: min={scores.min():.4f}, max={scores.max():.4f}, mean={scores.mean():.4f}")
```

### 6. **在 `lib/detectors/base_detector.py` 的 `post_process` 方法中**
- **位置**: 方法开始处
- **目的**: 检查后处理前的检测状态
- **添加代码**:
```python
# 在方法开始处
if len(dets) > 0 and len(dets[0]) > 0:
    scores = dets[0][:, 8] if len(dets[0].shape) == 2 else dets[0][:, :, 8]
    print(f"[INFERENCE] Pre-post-process detections: {len(scores)}")
    print(f"[INFERENCE] Score distribution: min={scores.min():.4f}, max={scores.max():.4f}, mean={scores.mean():.4f}")
else:
    print(f"[INFERENCE] No detections to post-process")
```

### 7. **在 `src/lib/models/networks/dla.py` 或对应的backbone文件中**
- **位置**: backbone的forward方法末尾
- **目的**: 检查backbone特征质量
- **添加代码**:
```python
# 在返回输出前
print(f"[INFERENCE] Backbone feature analysis:")
for key, value in ret.items():
    if hasattr(value, 'min') and hasattr(value, 'max'):
        print(f"[INFERENCE]   {key} range: {value.min():.4f} - {value.max():.4f}")
        if torch.isnan(value).any():
            print(f"[INFERENCE]   WARNING: {key} contains NaN values!")
        if torch.isinf(value).any():
            print(f"[INFERENCE]   WARNING: {key} contains Inf values!")
```

### 8. **在权重加载的地方**
- **位置**: `lib/detectors/base_detector.py` 的 `__init__` 方法中
- **目的**: 确认权重正确加载
- **添加代码**:
```python
# 在权重加载后
print(f"[INFERENCE] Weight loading verification:")
print(f"[INFERENCE]   Model loaded from: {opt.load_model}")
print(f"[INFERENCE]   Processor loaded from: {opt.load_processor}")

# 检查一些关键层的权重（如果存在）
if hasattr(self.model, 'hm') and hasattr(self.model.hm, 'weight'):
    hm_weight = self.model.hm.weight
    print(f"[INFERENCE]   HM layer weight range: {hm_weight.min():.4f} - {hm_weight.max():.4f}")
    print(f"[INFERENCE]   HM layer weight std: {hm_weight.std():.4f}")

# 检查权重是否为零（可能表示加载失败）
total_params = sum(p.numel() for p in self.model.parameters())
zero_params = sum((p == 0).sum().item() for p in self.model.parameters())
print(f"[INFERENCE]   Total parameters: {total_params}")
print(f"[INFERENCE]   Zero parameters: {zero_params} ({zero_params/total_params*100:.2f}%)")
```

## 调试优先级

### **最重要** (必须添加):
- **第1、2、3点**: 检查backbone输出和sigmoid后的heatmap
- **第5点**: 检查最终检测结果
- **第8点**: 确认权重加载

### **次重要** (建议添加):
- **第4、6点**: 检查中间处理步骤

### **可选** (深度调试时添加):
- **第7点**: 检查backbone内部特征

## 常见问题诊断

### 1. **分数过低问题**
- **现象**: 所有检测分数都在0.1左右
- **检查**: 第1、2、3点的输出
- **可能原因**: 权重未正确加载或模型未训练

### 2. **无检测结果**
- **现象**: 没有任何有效检测
- **检查**: 第3、4、5点的输出
- **可能原因**: 阈值设置过高或heatmap预测失效

### 3. **坐标异常**
- **现象**: 检测坐标超出合理范围
- **检查**: 第5、6点的输出
- **可能原因**: decode过程有误或坐标转换错误

### 4. **权重加载问题**
- **现象**: 模型输出接近随机
- **检查**: 第8点的输出
- **可能原因**: 权重文件路径错误或权重不匹配

## 使用建议

1. **按优先级逐步添加**: 先添加最重要的调试点
2. **对比正常输出**: 与已知正常工作的模型输出对比
3. **保存调试日志**: 将调试输出保存到文件便于分析
4. **移除调试代码**: 问题解决后及时移除调试代码

## 预期的正常输出范围

- **hm (sigmoid前)**: 目标区域 > 0, 背景区域 < -2
- **hm (sigmoid后)**: 目标区域 > 0.5, 背景区域 < 0.1
- **检测分数**: 有效检测通常 > 0.3
- **坐标范围**: 应在图像尺寸范围内

通过这些调试信息，可以快速定位LORE推理过程中的问题并进行针对性修复。

# LORE Debug信息实现总结

## 概述

已按照 `LORE_DEBUG_GUIDE.md` 的要求，将所有debug信息从控制台输出改为保存到 `demo_dir` 中的txt文件。

## 实现方案

### 1. Debug日志管理器 (`src/lib/utils/debug_logger.py`)

创建了一个专门的debug日志管理器，功能包括：
- 自动创建带时间戳的debug日志文件
- 支持按图像名称命名日志文件
- 提供分类标签的日志记录
- 自动处理文件写入异常

**日志文件命名规则**:
- 格式: `debug_{image_name}_{timestamp}.txt`
- 示例: `debug_image001_20241210_143052.txt`

### 2. 修改的文件和位置

#### 2.1 `src/lib/detectors/base_detector.py`
- **初始化debug logger**: 在 `run()` 方法开始处初始化
- **权重加载验证**: 记录模型和处理器加载信息、参数统计
- **Backbone输出检查**: 记录hm、wh、reg的数值范围
- **预处理参数**: 记录图像尺寸、center、scale、变换矩阵
- **关闭debug logger**: 在推理结束时关闭日志

#### 2.2 `src/lib/detectors/ctdet.py`
- **原始backbone输出**: 记录模型forward后的原始hm、wh范围
- **Sigmoid后heatmap**: 记录sigmoid后的激活统计
- **后处理前检测**: 记录进入后处理的检测数量和分数分布

#### 2.3 `src/lib/models/decode.py`
- **Top-K检测分数**: 记录最佳分数、分数范围、有效检测数量
- **最终检测结果**: 记录不同阈值下的检测统计和分数分布

#### 2.4 `src/lib/models/networks/pose_dla_dcn.py`
- **Backbone特征质量**: 记录各个head的输出范围，检测NaN和Inf值

#### 2.5 `src/lib/utils/image.py`
- **坐标转换过程**: 记录transform_preds的输入输出、变换矩阵、坐标范围

#### 2.6 `src/lib/utils/post_process.py`
- **后处理坐标转换**: 记录转换前后的坐标范围、按类别的坐标统计

## 3. Debug信息分类

所有debug信息按以下类别进行标记：

| 类别 | 说明 |
|------|------|
| `INFERENCE_START` | 推理开始信息 |
| `WEIGHT_LOADING` | 权重加载验证 |
| `PREPROCESS` | 预处理参数 |
| `RAW_BACKBONE` | 原始backbone输出 |
| `BACKBONE_OUTPUT` | 处理后的backbone输出 |
| `BACKBONE_FEATURES` | Backbone特征质量 |
| `SIGMOID_HEATMAP` | Sigmoid后的heatmap |
| `TOPK_SCORES` | Top-K检测分数 |
| `FINAL_DETECTIONS` | 最终检测结果 |
| `PRE_POST_PROCESS` | 后处理前状态 |
| `COORD_TRANSFORM` | 坐标转换过程 |
| `POST_PROCESS` | 后处理坐标转换 |
| `INFERENCE_END` | 推理结束信息 |

## 4. 日志文件结构

```
debug_image001_20241210_143052.txt
================================================================================
LORE INFERENCE DEBUG LOG
================================================================================
Timestamp: 2024-12-10 14:30:52
Image: /path/to/image001.jpg
Demo Directory: /path/to/demo_dir
================================================================================

[14:30:52.123] [INFERENCE_START] Starting LORE inference process
[14:30:52.124] [WEIGHT_LOADING] Weight loading verification:
[14:30:52.125] [WEIGHT_LOADING]   Model loaded from: model.pth
...
[14:30:53.456] [INFERENCE_END] LORE inference process completed
================================================================================
DEBUG LOG END
================================================================================
```

## 5. 使用方法

### 5.1 运行推理
正常运行推理脚本，debug信息会自动保存到 `demo_dir` 中：

```bash
python demo.py ctdet \
    --demo_dir /path/to/output/debug/ \
    --demo /path/to/images/ \
    ...
```

### 5.2 查看debug日志
在 `demo_dir` 中查找以 `debug_` 开头的txt文件：

```bash
ls /path/to/output/debug/debug_*.txt
```

## 6. 错误处理

- 如果debug_logger模块导入失败，会自动跳过debug信息记录
- 如果文件写入失败，会将错误信息输出到控制台
- 所有debug功能都不会影响正常的推理流程

## 7. 性能影响

- Debug信息记录对推理性能影响极小
- 文件I/O操作异步进行，不阻塞推理流程
- 可以通过删除debug相关代码完全禁用debug功能

## 8. 调试优势

1. **持久化存储**: debug信息保存在文件中，便于后续分析
2. **结构化记录**: 按时间戳和类别组织，便于查找问题
3. **完整追踪**: 从权重加载到最终结果的完整调试链
4. **坐标转换详情**: 详细记录坐标转换的每个步骤
5. **批量分析**: 可以对多个图像的debug日志进行批量分析

## 9. 故障排除

### 9.1 常见问题
- **日志文件未生成**: 检查demo_dir路径是否存在写权限
- **debug信息不完整**: 检查是否有import错误
- **文件名冲突**: 时间戳精确到毫秒，避免文件名冲突

### 9.2 验证方法
运行推理后检查：
1. demo_dir中是否生成了debug_*.txt文件
2. 文件内容是否包含所有预期的debug类别
3. 坐标转换信息是否完整记录

通过这个实现，所有的debug信息都会自动保存到demo_dir中，便于后续的问题诊断和分析。

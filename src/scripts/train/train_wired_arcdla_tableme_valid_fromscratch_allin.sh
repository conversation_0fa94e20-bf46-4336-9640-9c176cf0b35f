python main.py ctdet \
  --dataset table_labelmev2 \
  --dataset_name TableLabelMe \
	--exp_id train_tableme_all_wired_valid_fromscratch \
	--data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_all_wired_valid_configs.py \
  --config_name tableme_full \
	--wiz_4ps \
	--wiz_stacking \
	--wiz_pairloss \
	--tsfm_layers 3 \
	--stacking_layers 3 \
	--batch_size 30 \
	--master_batch_size 6 \
	--arch dla_34 \
	--lr 2e-5 \
	--K 500 \
	--MK 1000 \
	--num_epochs 85 \
	--lr_step '55, 75' \
	--gpus 0,1,2,3 \
	--num_workers 16 \
	--val_intervals 5 \
	--enable_data_cache \
	--save_all \
	--resume \
	--load_model /aipdf-mlp/lanx/workspace/experiment_results/LORE/ctdet/train_tableme_all_wired_valid_fromscratch/model_last.pth \
	--load_processor /aipdf-mlp/lanx/workspace/experiment_results/LORE/ctdet/train_tableme_all_wired_valid_fromscratch/processor_last.pth



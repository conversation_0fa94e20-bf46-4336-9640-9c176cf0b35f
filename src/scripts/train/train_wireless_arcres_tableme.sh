python main.py ctdet \
--dataset table_labelmev2 \
--dataset_name TableLabelMe \
--data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_all_release_configs.py \
--config_name tableme_full \
--exp_id train_tableme_all \
--wiz_2dpe \
--wiz_stacking \
--tsfm_layers 4 \
--stacking_layers 4 \
--batch_size 20 \
--master_batch_size 8 \
--arch resfpnhalf_18 \
--lr 1e-4 \
--K 500 \
--MK 1000 -\
-num_epochs 200 \
--lr_step '100, 160' \
--gpus 0,1 \
--num_workers 4 \
--prefetch_factor 2 \
--val_intervals 5 \
--enable_data_cache

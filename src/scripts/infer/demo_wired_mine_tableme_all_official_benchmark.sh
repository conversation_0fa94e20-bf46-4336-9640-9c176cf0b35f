python demo.py ctdet \
        --dataset table \
        --demo /aipdf-mlp/lanx/workspace/datasets/wired_optimizer/images \
        --demo_name demo_wired_benchmark_official \
        --debug 1 \
        --arch dla_34 \
        --K 3000 \
        --MK 5000 \
        --tsfm_layers 4 \
        --stacking_layers 4 \
        --gpus 0\
        --wiz_4ps \
        --wiz_detect \
        --wiz_rev \
        --wiz_stacking \
        --convert_onnx 0 \
        --vis_thresh_corner 0.3 \
        --vis_thresh 0.20 \
        --scores_thresh 0.2 \
        --nms \
        --demo_dir ../visualization_wired_official_epoch100_benchmark/ \
        --load_model /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/ckpt_wired/ckpt_wtw/model_best.pth \
        --load_processor /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/ckpt_wired/ckpt_wtw/processor_best.pth
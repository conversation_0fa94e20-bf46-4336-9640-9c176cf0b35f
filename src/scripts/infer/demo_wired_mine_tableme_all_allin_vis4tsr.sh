python demo.py ctdet \
        --dataset table \
        --demo ../input_images/vis4tsr \
        --demo_name demo_wired \
        --debug 1 \
        --arch dla_34 \
        --K 3000 \
        --MK 5000 \
        --tsfm_layers 3 \
        --stacking_layers 3 \
        --gpus 0\
        --wiz_4ps \
        --wiz_detect \
        --wiz_rev \
        --wiz_stacking \
        --convert_onnx 0 \
        --vis_thresh_corner 0.3 \
        --vis_thresh 0.20 \
        --scores_thresh 0.2 \
        --nms \
        --demo_dir ../visualization_wired_allin_epoch4_vis4tsr/ \
        --load_model /aipdf-mlp/lanx/workspace/experiment_results/LORE/ctdet/train_tableme_all_wired_valid_resumeofficial/model_best.pth \
        --load_processor /aipdf-mlp/lanx/workspace/experiment_results/LORE/ctdet/train_tableme_all_wired_valid_resumeofficial/processor_best.pth

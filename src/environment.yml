name: lore
channels:
  - conda-forge
  - pytorch
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - archspec=0.2.2=pyhd8ed1ab_0
  - binutils_impl_linux-64=2.40=hf600244_0
  - boltons=23.1.1=pyhd8ed1ab_0
  - brotli-python=1.1.0=py310hc6cd4ac_1
  - bzip2=1.0.8=hd590300_5
  - c-ares=1.24.0=hd590300_0
  - ca-certificates=2025.1.31=hbcca054_0
  - certifi=2025.1.31=pyhd8ed1ab_0
  - cffi=1.16.0=py310h2fee648_0
  - charset-normalizer=3.3.2=pyhd8ed1ab_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - conda-package-handling=2.2.0=pyh38be061_0
  - conda-package-streaming=0.9.0=pyhd8ed1ab_0
  - distro=1.8.0=pyhd8ed1ab_0
  - fmt=10.1.1=h00ab1b0_1
  - gcc=12.2.0=h26027b1_13
  - gcc_impl_linux-64=12.2.0=hcc96c02_19
  - icu=73.2=h59595ed_0
  - idna=3.6=pyhd8ed1ab_0
  - jsonpatch=1.33=pyhd8ed1ab_0
  - jsonpointer=2.4=py310hff52083_3
  - kernel-headers_linux-64=3.10.0=he073ed8_18
  - keyutils=1.6.1=h166bdaf_0
  - krb5=1.21.2=h659d440_0
  - ld_impl_linux-64=2.40=h41732ed_0
  - libarchive=3.7.2=h2aa1ff5_1
  - libcurl=8.5.0=hca28451_0
  - libedit=3.1.20191231=he28a2e2_2
  - libev=4.33=hd590300_2
  - libffi=3.4.2=h7f98852_5
  - libgcc=14.2.0=h767d61c_2
  - libgcc-devel_linux-64=12.2.0=h3b97bd3_19
  - libgcc-ng=14.2.0=h69a702a_2
  - libgomp=14.2.0=h767d61c_2
  - libiconv=1.17=hd590300_2
  - libmamba=1.5.5=had39da4_0
  - libmambapy=1.5.5=py310h39ff949_0
  - libnghttp2=1.58.0=h47da74e_1
  - libnsl=2.0.1=hd590300_0
  - libsanitizer=12.2.0=h46fd767_19
  - libsolv=0.7.27=hfc55251_0
  - libsqlite=3.44.2=h2797004_0
  - libssh2=1.11.0=h0841786_0
  - libstdcxx-ng=13.2.0=h7e041cc_3
  - libuuid=2.38.1=h0b41bf4_0
  - libxml2=2.12.3=h232c23b_0
  - libzlib=1.2.13=hd590300_5
  - lz4-c=1.9.4=hcb278e6_0
  - lzo=2.10=h516909a_1000
  - menuinst=2.0.1=py310hff52083_0
  - ncurses=6.4=h59595ed_2
  - openssl=3.4.1=h7b32b05_0
  - packaging=23.2=pyhd8ed1ab_0
  - pip=23.3.2=pyhd8ed1ab_0
  - platformdirs=4.1.0=pyhd8ed1ab_0
  - pluggy=1.3.0=pyhd8ed1ab_0
  - pybind11-abi=4=hd8ed1ab_3
  - pycosat=0.6.6=py310h2372a71_0
  - pycparser=2.21=pyhd8ed1ab_0
  - pysocks=1.7.1=pyha2e5f31_6
  - python=3.10.13=hd12c33a_0_cpython
  - python_abi=3.10=4_cp310
  - readline=8.2=h8228510_1
  - reproc=14.2.4.post0=hd590300_1
  - reproc-cpp=14.2.4.post0=h59595ed_1
  - requests=2.31.0=pyhd8ed1ab_0
  - ruamel.yaml=0.18.5=py310h2372a71_0
  - ruamel.yaml.clib=0.2.7=py310h2372a71_2
  - setuptools=68.2.2=pyhd8ed1ab_0
  - sysroot_linux-64=2.17=h0157908_18
  - tk=8.6.13=noxft_h4845f30_101
  - tqdm=4.66.1=pyhd8ed1ab_0
  - truststore=0.8.0=pyhd8ed1ab_0
  - urllib3=2.1.0=pyhd8ed1ab_0
  - wheel=0.42.0=pyhd8ed1ab_0
  - xz=5.2.6=h166bdaf_0
  - yaml-cpp=0.8.0=h59595ed_0
  - zstandard=0.22.0=py310h1275a96_0
  - zstd=1.5.5=hfc55251_0
  - pip:
      - absl-py==2.1.0
      - accelerate==0.26.1
      - adaseq==0.6.6
      - addict==2.4.0
      - aiofiles==23.2.1
      - aiohappyeyeballs==2.6.1
      - aiohttp==3.11.13
      - aiosignal==1.3.2
      - albucore==0.0.16
      - albumentations==1.4.10
      - aliyun-python-sdk-core==2.16.0
      - aliyun-python-sdk-kms==2.16.5
      - altair==5.5.0
      - annotated-types==0.7.0
      - antlr4-python3-runtime==4.9.3
      - anyio==4.8.0
      - appdirs==1.4.4
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - astor==0.8.1
      - asttokens==3.0.0
      - astunparse==1.6.3
      - async-lru==2.0.4
      - async-timeout==4.0.3
      - attrs==25.3.0
      - babel==2.17.0
      - backoff==2.2.1
      - baker==1.3
      - basicsr==1.4.2
      - beautifulsoup4==4.13.3
      - bitsandbytes==0.42.0
      - bleach==6.2.0
      - cachetools==5.5.2
      - chardet==5.2.0
      - chinese-calendar==1.10.0
      - click==8.1.8
      - coloredlogs==15.0.1
      - colorlog==6.9.0
      - comm==0.2.2
      - contourpy==1.3.1
      - crcmod==1.7
      - cryptography==43.0.3
      - cssselect==1.3.0
      - cssutils==2.11.1
      - cycler==0.12.1
      - cython==3.0.12
      - dataclasses-json==0.6.7
      - datasets==2.18.0
      - debugpy==1.8.13
      - decorator==5.1.1
      - decord==0.6.0
      - deepspeed==0.12.6
      - defusedxml==0.7.1
      - diffusers==0.25.0
      - dill==0.3.8
      - docker-pycreds==0.4.0
      - easydict==1.13
      - einops==0.7.0
      - emoji==2.14.1
      - et-xmlfile==2.0.0
      - exceptiongroup==1.2.2
      - executing==2.2.0
      - fairscale==0.4.13
      - faiss-cpu==1.8.0.post1
      - fastapi==0.115.11
      - fastjsonschema==2.21.1
      - ffmpy==0.5.0
      - filelock==3.13.1
      - filetype==1.2.0
      - fire==0.7.0
      - flash-attn==2.5.2
      - flatbuffers==25.2.10
      - fonttools==4.56.0
      - fqdn==1.5.1
      - frozenlist==1.5.0
      - fsspec==2024.2.0
      - ftfy==6.3.1
      - future==1.0.0
      - gast==0.6.0
      - gitdb==4.0.12
      - gitpython==3.1.44
      - google-auth==2.38.0
      - google-auth-oauthlib==1.2.1
      - google-pasta==0.2.0
      - gputil==1.4.0
      - gradio==3.44.3
      - gradio-client==0.5.0
      - greenlet==3.2.2
      - grpcio==1.71.0
      - h11==0.14.0
      - h5py==3.13.0
      - hf-xet==1.1.3
      - hjson==3.1.0
      - html5lib==1.1
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.32.4
      - humanfriendly==10.0
      - imageio==2.33.1
      - imagesize==1.4.1
      - imgaug==0.4.0
      - importlib-metadata==8.6.1
      - importlib-resources==6.5.2
      - insightface==0.7.3
      - ipykernel==6.29.5
      - ipython==8.34.0
      - isoduration==20.11.0
      - jedi==0.19.2
      - jieba==0.42.1
      - jinja2==3.1.4
      - jiter==0.10.0
      - jmespath==0.10.0
      - joblib==1.4.2
      - json5==0.10.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - jupyter-client==8.6.3
      - jupyter-core==5.7.2
      - jupyter-events==0.12.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.15.0
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.0.12
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - kaggle==*******
      - keras==2.15.0
      - kiwisolver==1.4.8
      - kornia==0.7.1
      - langchain==0.2.17
      - langchain-community==0.2.17
      - langchain-core==0.2.43
      - langchain-openai==0.1.25
      - langchain-text-splitters==0.2.4
      - langdetect==1.0.9
      - langsmith==0.1.147
      - lazy-loader==0.4
      - libclang==18.1.1
      - llvmlite==0.44.0
      - lmdb==1.6.2
      - logzero==1.7.0
      - lxml==5.4.0
      - markdown==3.7
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - marshmallow==3.26.1
      - matplotlib==3.7.4
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mistune==3.1.2
      - ml-dtypes==0.2.0
      - mmcv-full==1.7.0+torch2.1.1cu121
      - mmdet==2.28.2
      - mmengine==0.10.1
      - mmsegmentation==1.1.1
      - mnn==3.1.2
      - modelscope==1.13.1
      - more-itertools==10.7.0
      - mpmath==1.3.0
      - multidict==6.1.0
      - multiprocess==0.70.16
      - mypy-extensions==1.1.0
      - narwhals==1.30.0
      - nbclient==0.10.2
      - nbconvert==7.16.6
      - nbformat==5.10.4
      - ncnn==1.0.20240820
      - nest-asyncio==1.6.0
      - networkx==3.3
      - ninja==********
      - nltk==3.9.1
      - notebook-shim==0.2.4
      - numba==0.61.2
      - numpy==1.24.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.3.101
      - nvidia-cuda-nvrtc-cu12==12.3.107
      - nvidia-cuda-runtime-cu12==12.3.101
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-cufile-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==**********
      - nvidia-cusparselt-cu12==0.6.3
      - nvidia-ml-py==12.570.86
      - nvidia-nccl-cu12==2.19.3
      - nvidia-nvjitlink-cu12==12.6.85
      - nvidia-nvtx-cu12==12.4.127
      - oauthlib==3.2.2
      - olefile==0.47
      - omegaconf==2.3.0
      - onnx==1.15.0
      - onnxruntime==1.16.3
      - openai==1.63.2
      - opencv-contrib-python==4.10.0.84
      - opencv-python==4.9.0.80
      - opencv-python-headless==4.9.0.80
      - openpyxl==3.1.5
      - opt-einsum==3.3.0
      - orjson==3.10.15
      - oss2==2.19.1
      - overrides==7.7.0
      - paddleocr==2.10.0
      - paddlepaddle-gpu==3.0.0rc0
      - paddlex==3.0.0
      - pandas==2.1.4
      - pandocfilters==1.5.1
      - parsley==1.3
      - parso==0.8.4
      - peft==0.7.1
      - pexpect==4.9.0
      - pillow==10.2.0
      - pnnx==20240819
      - portalocker==3.1.1
      - premailer==3.10.0
      - prettytable==3.15.1
      - prodigyopt==1.0
      - progress==1.6.1
      - prometheus-client==0.21.1
      - prompt-toolkit==3.0.50
      - propcache==0.3.0
      - protobuf==4.25.6
      - psutil==7.0.0
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - py-cpuinfo==9.0.0
      - pyarrow==19.0.1
      - pyarrow-hotfix==0.6
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.1
      - pyclipper==1.3.0.post6
      - pycocotools==2.0.8
      - pycryptodome==3.21.0
      - pydantic==2.10.6
      - pydantic-core==2.27.2
      - pydub==0.25.1
      - pygments==2.19.1
      - pymupdf==1.25.1
      - pynvml==12.0.0
      - pyparsing==3.2.1
      - pypdf==5.5.0
      - pypdfium2==4.30.1
      - pyquaternion==0.9.9
      - python-dateutil==2.9.0.post0
      - python-docx==1.1.2
      - python-iso639==2025.2.18
      - python-json-logger==3.3.0
      - python-magic==0.4.27
      - python-multipart==0.0.20
      - python-oxmsg==0.0.2
      - python-slugify==8.0.4
      - pytorch-lightning==1.2.9
      - pytz==2025.1
      - pyyaml==6.0.2
      - pyzmq==26.3.0
      - rapidfuzz==3.13.0
      - referencing==0.36.2
      - regex==2024.11.6
      - requests-oauthlib==2.0.0
      - requests-toolbelt==1.0.0
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rich==13.9.4
      - rpds-py==0.23.1
      - rsa==4.9
      - safetensors==0.4.1
      - scikit-image==0.22.0
      - scikit-learn==1.3.2
      - scipy==1.11.1
      - semantic-version==2.10.0
      - send2trash==1.8.3
      - sentencepiece==0.2.0
      - sentry-sdk==2.22.0
      - seqeval==1.2.2
      - setproctitle==1.3.5
      - shapely==2.1.0
      - simplejson==3.20.1
      - simsimd==6.2.1
      - six==1.17.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - sortedcontainers==2.4.0
      - soundfile==0.13.1
      - soupsieve==2.6
      - sqlalchemy==2.0.41
      - stack-data==0.6.3
      - starlette==0.46.1
      - stringzilla==3.12.3
      - sympy==1.13.1
      - tb-nightly==2.20.0a20250315
      - tenacity==8.5.0
      - tensorboard==2.15.2
      - tensorboard-data-server==0.7.2
      - tensorflow==2.15.0
      - tensorflow-estimator==2.15.0
      - tensorflow-io-gcs-filesystem==0.37.1
      - termcolor==2.5.0
      - terminado==0.18.1
      - terminaltables==3.1.10
      - text-unidecode==1.3
      - threadpoolctl==3.6.0
      - tifffile==2025.3.13
      - tiktoken==0.9.0
      - timm==0.9.16
      - tinycss2==1.4.0
      - tokenizers==0.19.1
      - tomli==2.2.1
      - torch==2.1.2+cu121
      - torchaudio==2.1.2+cu121
      - torchmetrics==0.2.0
      - torchvision==0.16.2+cu121
      - tornado==6.4.2
      - traitlets==5.14.3
      - transformers==4.45.2
      - triton==2.1.0
      - types-python-dateutil==2.9.0.20241206
      - typing-extensions==4.12.2
      - typing-inspect==0.9.0
      - typing-inspection==0.4.1
      - tzdata==2025.1
      - ujson==5.10.0
      - unstructured==0.17.2
      - unstructured-client==0.35.0
      - uri-template==1.3.0
      - uvicorn==0.34.0
      - volcengine-python-sdk==1.0.124
      - voluptuous==0.14.2
      - wandb==0.16.2
      - wcwidth==0.2.13
      - webcolors==24.11.1
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - websockets==11.0.3
      - werkzeug==3.1.3
      - wrapt==1.14.1
      - xformers==0.0.23.post1
      - xxhash==3.5.0
      - yacs==0.1.8
      - yapf==0.43.0
      - yarl==1.18.3
      - zipp==3.21.0
prefix: /opt/miniforge3/envs/lore

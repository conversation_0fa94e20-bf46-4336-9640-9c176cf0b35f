# src/lib/utils/logger_config.py
"""
日志配置模块 - TableLabelMe数据格式支持项目
提供标准化的日志配置功能，与LORE-TSR现有日志系统保持一致
"""

import logging
import sys
from typing import Dict, Optional


class LoggerConfig:
    """日志配置管理器，提供标准化的日志配置功能"""
    
    @staticmethod
    def get_default_config() -> Dict:
        """
        获取默认日志配置

        Returns:
            Dict: 默认日志配置字典，包含级别、格式、输出选项等
        """
        return {
            "level": "INFO",
            "format": "[%(asctime)s] %(levelname)s [%(name)s] %(message)s",
            "date_format": "%Y-%m-%d %H:%M:%S",
            "console_output": True,
            "file_output": False,
            "log_file_path": None
        }
    
    @staticmethod
    def setup_logger(name: str, config: Optional[Dict] = None) -> logging.Logger:
        """
        根据配置创建和设置日志记录器
        
        Args:
            name (str): 日志记录器名称
            config (Optional[Dict]): 日志配置字典，如果为None则使用默认配置
            
        Returns:
            logging.Logger: 配置好的日志记录器实例
        """
        if config is None:
            config = LoggerConfig.get_default_config()
        
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, config["level"]))
        
        # 清除现有处理器，避免重复日志
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            config["format"], 
            datefmt=config["date_format"]
        )
        
        # 控制台处理器
        if config["console_output"]:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器（可选）
        if config["file_output"] and config["log_file_path"]:
            file_handler = logging.FileHandler(config["log_file_path"])
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger

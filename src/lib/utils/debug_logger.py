from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import os
import time
from datetime import datetime

class DebugLogger:
    """Debug信息日志管理器，将debug信息保存到指定目录的txt文件中"""
    
    def __init__(self, demo_dir, image_name=None):
        self.demo_dir = demo_dir
        self.image_name = image_name
        self.log_file = None
        self.session_start_time = time.time()
        
        # 确保demo_dir存在
        if not os.path.exists(demo_dir):
            os.makedirs(demo_dir)
        
        # 创建debug日志文件
        self._create_log_file()
    
    def _create_log_file(self):
        """创建debug日志文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if self.image_name:
            # 从图像路径中提取文件名（不含扩展名）
            base_name = os.path.splitext(os.path.basename(self.image_name))[0]
            log_filename = f"debug_{base_name}_{timestamp}.txt"
        else:
            log_filename = f"debug_inference_{timestamp}.txt"
        
        self.log_file = os.path.join(self.demo_dir, log_filename)
        
        # 写入日志头部信息
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("LORE INFERENCE DEBUG LOG\n")
            f.write("="*80 + "\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            if self.image_name:
                f.write(f"Image: {self.image_name}\n")
            f.write(f"Demo Directory: {self.demo_dir}\n")
            f.write("="*80 + "\n\n")
    
    def log(self, message, category="GENERAL"):
        """记录debug信息到文件"""
        if not self.log_file:
            return
        
        try:
            elapsed_time = time.time() - self.session_start_time
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] [{category}] {message}\n")
        except Exception as e:
            # 如果写入失败，至少打印到控制台
            print(f"[DEBUG_LOGGER_ERROR] Failed to write to log: {e}")
            print(f"[{category}] {message}")
    
    def log_section(self, section_name):
        """记录一个新的调试段落"""
        separator = "-" * 60
        self.log(separator)
        self.log(f"SECTION: {section_name}")
        self.log(separator)
    
    def log_subsection(self, subsection_name):
        """记录一个子段落"""
        self.log(f"--- {subsection_name} ---")
    
    def close(self):
        """关闭日志文件"""
        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write("\n" + "="*80 + "\n")
                    f.write("DEBUG LOG END\n")
                    f.write("="*80 + "\n")
            except:
                pass

# 全局debug logger实例
_global_debug_logger = None

def init_debug_logger(demo_dir, image_name=None):
    """初始化全局debug logger"""
    global _global_debug_logger
    _global_debug_logger = DebugLogger(demo_dir, image_name)
    return _global_debug_logger

def get_debug_logger():
    """获取全局debug logger"""
    return _global_debug_logger

def debug_log(message, category="GENERAL"):
    """便捷的debug日志记录函数"""
    if _global_debug_logger:
        _global_debug_logger.log(message, category)
    else:
        # 如果没有初始化logger，则打印到控制台
        print(f"[{category}] {message}")

def close_debug_logger():
    """关闭全局debug logger"""
    global _global_debug_logger
    if _global_debug_logger:
        _global_debug_logger.close()
        _global_debug_logger = None

"""
LORE-TSR 数据可视化集成示例
展示如何在 CTDetDataset 中集成可视化功能
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_visualizer import visualize_training_sample


class CTDetDatasetWithVisualization:
    """
    带可视化功能的 CTDetDataset 示例
    展示如何在现有的 __getitem__ 方法中集成可视化
    """
    
    def __init__(self, opt):
        """
        初始化数据集
        
        Args:
            opt: 配置对象，需要包含以下属性：
                - debug: 调试级别 (0: 关闭, 1: 基础, 2: 详细)
                - enable_visualization: 是否启用可视化
                - visualization_dir: 可视化保存目录
                - visualization_sample_rate: 可视化采样率 (0.0-1.0)
                - down_ratio: 下采样比例
        """
        self.opt = opt
        
        # 可视化相关配置
        self.enable_vis = getattr(opt, 'enable_visualization', False)
        self.vis_dir = getattr(opt, 'visualization_dir', 'debug_visualizations')
        self.vis_sample_rate = getattr(opt, 'visualization_sample_rate', 0.1)  # 10% 采样率
        self.vis_counter = 0
        
        # 确保可视化目录存在
        if self.enable_vis:
            os.makedirs(self.vis_dir, exist_ok=True)
            print(f"📊 可视化已启用，保存目录: {self.vis_dir}")
            print(f"📊 采样率: {self.vis_sample_rate*100:.1f}%")
    
    def __getitem__(self, index):
        """
        获取训练样本（集成可视化功能的版本）
        
        这里展示如何在现有的 __getitem__ 方法末尾添加可视化调用
        """
        # ========== 原有的数据处理逻辑 ==========
        # 这里是原有的 CTDetDataset.__getitem__ 的所有代码
        # 包括图像加载、预处理、标注解析、热力图生成等
        
        # 模拟原有的返回字典（实际使用时替换为真实的处理结果）
        ret = self._simulate_original_getitem(index)
        
        # ========== 新增的可视化逻辑 ==========
        if self.enable_vis and self._should_visualize():
            try:
                # 生成样本ID
                sample_id = f"sample_{index:06d}"
                
                # 调用可视化函数
                visualize_training_sample(
                    ret_dict=ret,
                    sample_id=sample_id,
                    save_dir=self.vis_dir,
                    down_ratio=self.opt.down_ratio,
                    mean=getattr(self.opt, 'mean', [0.408, 0.447, 0.470]),
                    std=getattr(self.opt, 'std', [0.289, 0.274, 0.278])
                )
                
                print(f"✅ 已保存样本 {sample_id} 的可视化结果")
                
            except Exception as e:
                print(f"⚠️ 样本 {index} 可视化失败: {e}")
        
        return ret
    
    def _should_visualize(self) -> bool:
        """
        判断是否应该对当前样本进行可视化
        
        Returns:
            bool: 是否进行可视化
        """
        self.vis_counter += 1
        
        # 基于采样率决定
        if self.vis_sample_rate >= 1.0:
            return True
        elif self.vis_sample_rate <= 0.0:
            return False
        else:
            # 每 1/sample_rate 个样本可视化一次
            interval = int(1.0 / self.vis_sample_rate)
            return (self.vis_counter % interval) == 0
    
    def _simulate_original_getitem(self, index):
        """
        模拟原有的 __getitem__ 返回结果
        实际使用时删除此方法，直接使用原有的处理逻辑
        """
        import numpy as np
        
        # 模拟数据维度（实际使用时替换为真实数据）
        input_h, input_w = 1024, 1024
        output_h, output_w = 256, 256
        max_objs = 300
        max_cors = 1000
        num_classes = 2
        
        # 模拟返回字典
        ret = {
            'input': np.random.randn(3, input_h, input_w).astype(np.float32),
            'hm': np.random.rand(num_classes, output_h, output_w).astype(np.float32),
            'hm_ind': np.random.randint(0, output_h*output_w, max_objs).astype(np.int64),
            'hm_mask': np.random.randint(0, 2, max_objs).astype(np.uint8),
            'mk_ind': np.random.randint(0, output_h*output_w, max_cors).astype(np.int64),
            'mk_mask': np.random.randint(0, 2, max_cors).astype(np.uint8),
            'reg': np.random.randn(max_objs*5, 2).astype(np.float32),
            'reg_ind': np.random.randint(0, output_h*output_w, max_objs*5).astype(np.int64),
            'reg_mask': np.random.randint(0, 2, max_objs*5).astype(np.uint8),
            'wh': np.random.randn(max_objs, 8).astype(np.float32) * 50,
            'st': np.random.randn(max_cors, 8).astype(np.float32) * 20,
            'ctr_cro_ind': np.random.randint(0, max_cors*4, max_objs*4).astype(np.int64),
            'cc_match': np.random.randint(0, max_cors, (max_objs, 4)).astype(np.int64),
            'hm_ctxy': np.random.rand(max_objs, 2).astype(np.float32) * output_w,
            'logic': np.random.randint(0, 10, (max_objs, 4)).astype(np.float32),
            'h_pair_ind': np.zeros(100).astype(np.int64),
            'v_pair_ind': np.zeros(100).astype(np.int64)
        }
        
        # 确保逻辑坐标的合理性
        for i in range(max_objs):
            if ret['hm_mask'][i] == 1:
                start_row = np.random.randint(0, 8)
                start_col = np.random.randint(0, 8)
                end_row = start_row + np.random.randint(0, 3)
                end_col = start_col + np.random.randint(0, 3)
                ret['logic'][i] = [start_row, end_row, start_col, end_col]
        
        return ret


def modify_existing_ctdet_dataset():
    """
    展示如何修改现有的 CTDetDataset 类
    
    在现有的 src/lib/datasets/sample/ctdet.py 文件中：
    
    1. 在文件顶部添加导入：
       from lib.utils.data_visualizer import visualize_training_sample
    
    2. 在 __init__ 方法中添加可视化配置：
       self.enable_vis = getattr(opt, 'enable_visualization', False)
       self.vis_dir = getattr(opt, 'visualization_dir', 'debug_visualizations')
       self.vis_sample_rate = getattr(opt, 'visualization_sample_rate', 0.1)
       self.vis_counter = 0
    
    3. 在 __getitem__ 方法的 return ret 之前添加：
       
       # 可视化功能（新增）
       if self.enable_vis and self._should_visualize():
           try:
               sample_id = f"sample_{index:06d}"
               visualize_training_sample(
                   ret_dict=ret,
                   sample_id=sample_id,
                   save_dir=self.vis_dir,
                   down_ratio=self.opt.down_ratio
               )
           except Exception as e:
               print(f"可视化失败: {e}")
       
       return ret
    
    4. 添加辅助方法：
       def _should_visualize(self) -> bool:
           self.vis_counter += 1
           if self.vis_sample_rate <= 0.0:
               return False
           interval = max(1, int(1.0 / self.vis_sample_rate))
           return (self.vis_counter % interval) == 0
    """
    pass


def example_usage_in_training():
    """
    展示在训练脚本中如何启用可视化
    
    在训练配置中添加以下参数：
    """
    
    # 方法1: 通过命令行参数
    example_args = """
    python main.py ctdet \
        --exp_id debug_with_vis \
        --enable_visualization \
        --visualization_dir ./debug_vis \
        --visualization_sample_rate 0.05 \
        --debug 1
    """
    
    # 方法2: 在配置文件中设置
    example_config = {
        'enable_visualization': True,
        'visualization_dir': './debug_visualizations',
        'visualization_sample_rate': 0.1,  # 10% 的样本进行可视化
        'debug': 1
    }
    
    print("示例命令行参数:")
    print(example_args)
    print("\n示例配置:")
    for key, value in example_config.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    # 运行示例
    print("🎨 LORE-TSR 数据可视化集成示例")
    print("=" * 50)
    
    # 展示配置示例
    example_usage_in_training()
    
    print("\n📝 集成步骤:")
    print("1. 将 data_visualizer.py 放入 src/lib/utils/ 目录")
    print("2. 在 CTDetDataset 中添加可视化调用")
    print("3. 在训练配置中启用可视化参数")
    print("4. 运行训练，检查生成的可视化结果")
    
    print("\n✅ 集成完成后，可视化结果将保存在指定目录中")
    print("   包括：输入图像、热力图、边界框、逻辑坐标等")

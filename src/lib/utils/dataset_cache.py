#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-28
# <AUTHOR> LORE-TSR Team
# @FileName: dataset_cache.py

import os
import json
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from .logger_config import LoggerConfig


class DatasetIndexCache:
    """
    数据集索引缓存管理器
    
    基于现有JSON缓存机制扩展，实现数据集级别的预处理索引缓存。
    解决FileScanner和QualityFilter的重复执行问题，将文件扫描和质量过滤的结果缓存到磁盘。
    """
    
    def __init__(self, opt):
        """
        初始化数据集索引缓存管理器
        
        Args:
            opt: 配置对象，包含缓存相关参数
        """
        # 重用现有配置参数
        self.enable_cache = getattr(opt, 'enable_data_cache', False)
        self.cache_size = getattr(opt, 'cache_size', 15000)
        
        # 使用现有的save_dir配置
        base_cache_dir = getattr(opt, 'save_dir', './cache')
        self.cache_dir = os.path.join(base_cache_dir, 'dataset_index_cache')
        
        # 使用现有的LoggerConfig日志系统
        self.logger = LoggerConfig.setup_logger('DatasetIndexCache')
        
        # 缓存统计信息
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'saves': 0,
            'total_load_time': 0.0,
            'total_save_time': 0.0
        }
        
        # 创建缓存目录
        if self.enable_cache:
            os.makedirs(self.cache_dir, exist_ok=True)
            self.logger.info(f'✓ 数据集索引缓存已启用，缓存目录: {self.cache_dir}')
        else:
            self.logger.info('数据集索引缓存未启用')
    
    def get_cached_index(self, data_paths: List[str], split: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的数据集索引
        
        Args:
            data_paths: 数据路径列表
            split: 数据分割（train/val/test）
            
        Returns:
            缓存的索引数据，如果缓存不存在或无效则返回None
        """
        if not self.enable_cache:
            return None
            
        start_time = time.time()
        
        try:
            cache_file = self._get_cache_file_path(data_paths, split)
            
            if not os.path.exists(cache_file):
                self.cache_stats['misses'] += 1
                self.logger.debug(f'缓存文件不存在: {cache_file}')
                return None
            
            # 检查缓存有效性
            if not self._is_cache_valid(cache_file, data_paths):
                self.cache_stats['misses'] += 1
                self.logger.info(f'缓存已过期，需要重新生成: {cache_file}')
                return None
            
            # 加载缓存数据
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            self.cache_stats['hits'] += 1
            load_time = time.time() - start_time
            self.cache_stats['total_load_time'] += load_time
            
            self.logger.info(f'✓ 成功加载缓存索引: {len(cache_data.get("file_index", {}))}个文件对 '
                           f'(加载时间: {load_time:.3f}s)')
            
            return cache_data
            
        except Exception as e:
            self.cache_stats['misses'] += 1
            self.logger.warning(f'加载缓存失败: {e}')
            return None
        finally:
            self.cache_stats['total_load_time'] += time.time() - start_time
    
    def save_index_cache(self, file_index: Dict[int, Dict[str, str]], 
                        scan_statistics: Dict[str, Any],
                        filter_statistics: Dict[str, Any],
                        exception_report: Dict[str, Any],
                        data_paths: List[str], 
                        split: str) -> bool:
        """
        保存数据集索引到缓存
        
        Args:
            file_index: 文件索引数据
            scan_statistics: 扫描统计信息
            filter_statistics: 过滤统计信息
            exception_report: 异常报告
            data_paths: 数据路径列表
            split: 数据分割
            
        Returns:
            是否保存成功
        """
        if not self.enable_cache:
            return False
            
        start_time = time.time()
        
        try:
            cache_file = self._get_cache_file_path(data_paths, split)
            
            # 构建缓存数据
            cache_data = {
                'file_index': file_index,
                'scan_statistics': scan_statistics,
                'filter_statistics': filter_statistics,
                'exception_report': exception_report,
                'metadata': {
                    'created_time': time.time(),
                    'data_paths': data_paths,
                    'split': split,
                    'checksum': self._calculate_checksum(data_paths),
                    'total_files': len(file_index),
                    'cache_version': '1.0'
                }
            }
            
            # 保存到临时文件，然后原子性重命名
            temp_file = cache_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
            os.rename(temp_file, cache_file)
            
            self.cache_stats['saves'] += 1
            save_time = time.time() - start_time
            self.cache_stats['total_save_time'] += save_time
            
            # 计算缓存文件大小
            cache_size_mb = os.path.getsize(cache_file) / (1024 * 1024)
            
            self.logger.info(f'✓ 成功保存缓存索引: {len(file_index)}个文件对 '
                           f'(保存时间: {save_time:.3f}s, 文件大小: {cache_size_mb:.1f}MB)')
            
            return True
            
        except Exception as e:
            self.logger.error(f'保存缓存失败: {e}')
            # 清理临时文件
            temp_file = cache_file + '.tmp'
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            return False
        finally:
            self.cache_stats['total_save_time'] += time.time() - start_time
    
    def _get_cache_file_path(self, data_paths: List[str], split: str) -> str:
        """
        生成缓存文件路径
        
        Args:
            data_paths: 数据路径列表
            split: 数据分割
            
        Returns:
            缓存文件路径
        """
        # 基于数据路径和分割生成唯一的缓存文件名
        path_hash = self._calculate_checksum(data_paths)
        cache_filename = f'index_{split}_{path_hash[:8]}.json'
        return os.path.join(self.cache_dir, cache_filename)
    
    def _is_cache_valid(self, cache_file: str, data_paths: List[str]) -> bool:
        """
        检查缓存是否有效
        
        Args:
            cache_file: 缓存文件路径
            data_paths: 数据路径列表
            
        Returns:
            缓存是否有效
        """
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            metadata = cache_data.get('metadata', {})
            
            # 检查数据路径是否变化
            cached_paths = metadata.get('data_paths', [])
            if cached_paths != data_paths:
                self.logger.debug('数据路径已变化，缓存无效')
                return False
            
            # 检查校验和
            current_checksum = self._calculate_checksum(data_paths)
            cached_checksum = metadata.get('checksum', '')
            if current_checksum != cached_checksum:
                self.logger.debug('数据路径校验和不匹配，缓存无效')
                return False
            
            # 检查目录修改时间
            cache_time = metadata.get('created_time', 0)
            for path in data_paths:
                if os.path.exists(path) and os.path.getmtime(path) > cache_time:
                    self.logger.debug(f'目录 {path} 已修改，缓存无效')
                    return False
            
            return True
            
        except Exception as e:
            self.logger.debug(f'缓存有效性检查失败: {e}')
            return False
    
    def _calculate_checksum(self, data_paths: List[str]) -> str:
        """
        计算数据路径的校验和
        
        Args:
            data_paths: 数据路径列表
            
        Returns:
            校验和字符串
        """
        # 对路径列表进行排序以确保一致性
        sorted_paths = sorted(data_paths)
        path_string = '|'.join(sorted_paths)
        return hashlib.md5(path_string.encode('utf-8')).hexdigest()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        if not self.enable_cache:
            return {'cache_enabled': False}
        
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_enabled': True,
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'saves': self.cache_stats['saves'],
            'hit_rate': hit_rate,
            'total_load_time': self.cache_stats['total_load_time'],
            'total_save_time': self.cache_stats['total_save_time'],
            'avg_load_time': (self.cache_stats['total_load_time'] / self.cache_stats['hits']) 
                           if self.cache_stats['hits'] > 0 else 0,
            'cache_dir': self.cache_dir
        }
    
    def print_cache_stats(self):
        """打印缓存统计信息"""
        stats = self.get_cache_stats()
        
        if not stats['cache_enabled']:
            self.logger.info('数据集索引缓存未启用')
            return
        
        self.logger.info('=== 数据集索引缓存统计 ===')
        self.logger.info(f'缓存命中: {stats["hits"]} 次')
        self.logger.info(f'缓存未命中: {stats["misses"]} 次')
        self.logger.info(f'缓存保存: {stats["saves"]} 次')
        self.logger.info(f'命中率: {stats["hit_rate"]:.1f}%')
        self.logger.info(f'总加载时间: {stats["total_load_time"]:.3f}s')
        self.logger.info(f'平均加载时间: {stats["avg_load_time"]:.3f}s')
        self.logger.info(f'缓存目录: {stats["cache_dir"]}')
    
    def clear_cache(self, split: Optional[str] = None):
        """
        清理缓存文件
        
        Args:
            split: 指定要清理的分割，如果为None则清理所有缓存
        """
        if not self.enable_cache:
            return
        
        try:
            if not os.path.exists(self.cache_dir):
                return
            
            cleared_count = 0
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.json'):
                    if split is None or f'index_{split}_' in filename:
                        file_path = os.path.join(self.cache_dir, filename)
                        os.remove(file_path)
                        cleared_count += 1
            
            self.logger.info(f'✓ 清理了 {cleared_count} 个缓存文件')
            
        except Exception as e:
            self.logger.error(f'清理缓存失败: {e}')

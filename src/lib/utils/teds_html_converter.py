"""
TEDS HTML转换器

将LORE项目的数据格式转换为TEDS计算所需的HTML格式

Author: LORE-TSR-adapt Project
Date: 2025-01-24
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)


class TEDSHTMLConverter:
    """
    TEDS HTML转换器
    
    负责将LORE项目的各种数据格式转换为TEDS计算所需的HTML表格格式
    """
    
    def __init__(self, include_content: bool = True, include_header_info: bool = True):
        """
        初始化转换器
        
        Args:
            include_content: 是否在HTML中包含单元格内容
            include_header_info: 是否区分表头和数据单元格
        """
        self.include_content = include_content
        self.include_header_info = include_header_info
    
    def convert_tableme_format_to_html(self, data: Dict[str, Any]) -> str:
        """
        将TableMe格式数据转换为HTML
        
        Args:
            data: TableMe格式数据，包含cells数组
            
        Returns:
            str: HTML表格字符串
        """
        try:
            if 'cells' not in data:
                logger.warning("数据中缺少cells字段")
                return "<table></table>"
            
            cells = data['cells']
            if not cells:
                return "<table></table>"
            
            # 1. 解析单元格逻辑位置，构建表格网格
            grid_info = self._build_grid_from_cells(cells)
            
            # 2. 生成HTML表格
            html = self._generate_html_from_grid(grid_info)
            
            return html
            
        except Exception as e:
            logger.error(f"转换TableMe格式到HTML时出错: {e}")
            return "<table></table>"
    
    def convert_lore_predictions_to_html(self, predictions: Dict[str, Any]) -> str:
        """
        将LORE模型预测结果转换为HTML
        
        Args:
            predictions: LORE预测结果，包含logic_coords, bboxes, scores等
            
        Returns:
            str: HTML表格字符串
        """
        try:
            logic_coords = predictions.get('logic_coords', [])
            scores = predictions.get('scores', [])
            
            if not logic_coords:
                return "<table></table>"
            
            # 过滤低置信度预测
            filtered_cells = []
            for i, (logic_coord, score) in enumerate(zip(logic_coords, scores)):
                if score > 0.5:  # 置信度阈值
                    cell_info = {
                        'cell_ind': i,
                        'lloc': {
                            'start_row': int(logic_coord[0]),
                            'end_row': int(logic_coord[1]),
                            'start_col': int(logic_coord[2]),
                            'end_col': int(logic_coord[3])
                        },
                        'content': [{'text': f'cell_{i}', 'bbox': None, 'direction': None, 'score': None}],
                        'header': False  # 默认为数据单元格
                    }
                    filtered_cells.append(cell_info)
            
            # 构建网格并生成HTML
            grid_info = self._build_grid_from_cells(filtered_cells)
            html = self._generate_html_from_grid(grid_info)
            
            return html
            
        except Exception as e:
            logger.error(f"转换LORE预测结果到HTML时出错: {e}")
            return "<table></table>"
    
    def convert_coco_annotations_to_html(self, annotations: List[Dict[str, Any]]) -> str:
        """
        将COCO格式标注转换为HTML
        
        Args:
            annotations: COCO格式标注列表，每个标注包含logic_axis字段
            
        Returns:
            str: HTML表格字符串
        """
        try:
            if not annotations:
                return "<table></table>"
            
            # 转换为统一的cell格式
            cells = []
            for i, ann in enumerate(annotations):
                logic_axis = ann.get('logic_axis', [[0, 0, 0, 0]])[0]
                
                cell_info = {
                    'cell_ind': i,
                    'lloc': {
                        'start_row': int(logic_axis[0]),
                        'end_row': int(logic_axis[1]) if logic_axis[1] > 0 else int(logic_axis[0]),
                        'start_col': int(logic_axis[2]),
                        'end_col': int(logic_axis[3]) if logic_axis[3] > 0 else int(logic_axis[2])
                    },
                    'content': [{'text': f'cell_{ann.get("id", i)}', 'bbox': None, 'direction': None, 'score': None}],
                    'header': False
                }
                cells.append(cell_info)
            
            # 构建网格并生成HTML
            grid_info = self._build_grid_from_cells(cells)
            html = self._generate_html_from_grid(grid_info)
            
            return html
            
        except Exception as e:
            logger.error(f"转换COCO标注到HTML时出错: {e}")
            return "<table></table>"
    
    def _build_grid_from_cells(self, cells: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        从单元格列表构建表格网格信息
        
        Args:
            cells: 单元格列表
            
        Returns:
            Dict: 网格信息，包含grid_cells和表格尺寸
        """
        grid_cells = {}
        max_row, max_col = 0, 0
        
        for cell in cells:
            lloc = cell.get('lloc', {})
            start_row = lloc.get('start_row', 0)
            end_row = lloc.get('end_row', start_row)
            start_col = lloc.get('start_col', 0)
            end_col = lloc.get('end_col', start_col)
            
            # 更新表格尺寸
            max_row = max(max_row, end_row)
            max_col = max(max_col, end_col)
            
            # 提取单元格内容
            content_text = ""
            if self.include_content:
                content_list = cell.get('content', [])
                if content_list and isinstance(content_list, list):
                    content_text = ' '.join([item.get('text', '') for item in content_list if item.get('text')])
                elif isinstance(content_list, str):
                    content_text = content_list
            
            # 计算跨行跨列
            rowspan = end_row - start_row + 1 if end_row > start_row else 1
            colspan = end_col - start_col + 1 if end_col > start_col else 1
            
            # 存储单元格信息
            grid_cells[(start_row, start_col)] = {
                'content': content_text,
                'rowspan': rowspan,
                'colspan': colspan,
                'is_header': cell.get('header', False),
                'cell_id': cell.get('cell_ind', 'unknown')
            }
        
        return {
            'grid_cells': grid_cells,
            'max_row': max_row,
            'max_col': max_col
        }
    
    def _generate_html_from_grid(self, grid_info: Dict[str, Any]) -> str:
        """
        从网格信息生成HTML表格
        
        Args:
            grid_info: 网格信息
            
        Returns:
            str: HTML表格字符串
        """
        grid_cells = grid_info['grid_cells']
        max_row = grid_info['max_row']
        max_col = grid_info['max_col']
        
        if not grid_cells:
            return "<table></table>"
        
        html_parts = ['<table>']
        
        # 生成表格行
        for row in range(max_row + 1):
            html_parts.append('<tr>')
            
            for col in range(max_col + 1):
                if (row, col) in grid_cells:
                    cell = grid_cells[(row, col)]
                    
                    # 选择标签类型
                    tag = 'th' if (self.include_header_info and cell['is_header']) else 'td'
                    
                    # 构建属性
                    attrs = []
                    if cell['rowspan'] > 1:
                        attrs.append(f'rowspan="{cell["rowspan"]}"')
                    if cell['colspan'] > 1:
                        attrs.append(f'colspan="{cell["colspan"]}"')
                    
                    attrs_str = ' ' + ' '.join(attrs) if attrs else ''
                    
                    # 添加单元格
                    content = cell['content'] if self.include_content else ''
                    html_parts.append(f'<{tag}{attrs_str}>{content}</{tag}>')
                    
                else:
                    # 检查是否被跨行跨列单元格占用
                    occupied = False
                    for (r, c), cell in grid_cells.items():
                        if (r <= row < r + cell['rowspan'] and 
                            c <= col < c + cell['colspan'] and 
                            (r, c) != (row, col)):
                            occupied = True
                            break
                    
                    # 如果没有被占用，添加空单元格
                    if not occupied:
                        html_parts.append('<td></td>')
            
            html_parts.append('</tr>')
        
        html_parts.append('</table>')
        return ''.join(html_parts)
    
    def validate_html(self, html: str) -> bool:
        """
        验证生成的HTML是否有效
        
        Args:
            html: HTML字符串
            
        Returns:
            bool: 是否有效
        """
        try:
            # 基本格式检查
            if not html.startswith('<table>') or not html.endswith('</table>'):
                return False
            
            # 检查是否包含基本的表格结构
            if '<tr>' not in html:
                return html == '<table></table>'  # 空表格是有效的
            
            # 检查标签配对
            tr_open = html.count('<tr>')
            tr_close = html.count('</tr>')
            if tr_open != tr_close:
                return False
            
            td_open = html.count('<td')
            td_close = html.count('</td>')
            th_open = html.count('<th')
            th_close = html.count('</th>')
            
            if td_open != td_close or th_open != th_close:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"HTML验证时出错: {e}")
            return False


def create_converter(structure_only: bool = False) -> TEDSHTMLConverter:
    """
    创建HTML转换器实例
    
    Args:
        structure_only: 是否只关注结构（不包含内容）
        
    Returns:
        TEDSHTMLConverter: 转换器实例
    """
    return TEDSHTMLConverter(
        include_content=not structure_only,
        include_header_info=True
    )


# 便捷函数
def tableme_to_html(data: Dict[str, Any], structure_only: bool = False) -> str:
    """将TableMe格式转换为HTML"""
    converter = create_converter(structure_only)
    return converter.convert_tableme_format_to_html(data)


def lore_predictions_to_html(predictions: Dict[str, Any], structure_only: bool = False) -> str:
    """将LORE预测结果转换为HTML"""
    converter = create_converter(structure_only)
    return converter.convert_lore_predictions_to_html(predictions)


def coco_annotations_to_html(annotations: List[Dict[str, Any]], structure_only: bool = False) -> str:
    """将COCO标注转换为HTML"""
    converter = create_converter(structure_only)
    return converter.convert_coco_annotations_to_html(annotations)

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import time
import torch
from progress.bar import Bar
from models.data_parallel import DataParallel
from utils.utils import AverageMeter
from models.classifier import Processor
from utils.adjacency import loss_mask


class ModleWithLoss(torch.nn.Module):
  def __init__(self, opt, model, loss, processor=None):
    super(ModleWithLoss, self).__init__()
    self.opt = opt
    
    self.model = model

    if self.opt.hold_det:
      for param in self.model.named_parameters():
        #if param[0] in need_frozen_list:
        param[1].requires_grad = False

    self.processor = processor
    self.loss = loss

  def forward(self, epoch, batch):
    
    outputs = self.model(batch['input'])
  
    if self.opt.wiz_stacking:
      logic_axis, stacked_axis = self.processor(outputs, batch)
      loss, loss_stats = self.loss(epoch, outputs, batch, logic_axis, stacked_axis)
    else:
      logic_axis = self.processor(outputs, batch)
      loss, loss_stats = self.loss(epoch, outputs, batch, logic_axis)
    
    return outputs[-1], loss, loss_stats

class BaseTrainer(object):
  def __init__(self, opt, model, optimizer=None, processor=None):
    self.opt = opt
    self.optimizer = optimizer
    self.loss_stats, self.loss = self._get_losses(opt)
    self.model_with_loss = ModleWithLoss(self.opt, model, self.loss, processor)
   

  def set_device(self, gpus, chunk_sizes, device):
    if len(gpus) > 1:
      self.model_with_loss = DataParallel(
        self.model_with_loss, device_ids=gpus, 
        chunk_sizes=chunk_sizes).to(device)
    else:
      self.model_with_loss = self.model_with_loss.to(device)
    
    for state in self.optimizer.state.values():
      for k, v in state.items():
        if isinstance(v, torch.Tensor):
          state[k] = v.to(device=device, non_blocking=True)

  def run_epoch(self, phase, epoch, data_loader):
    model_with_loss = self.model_with_loss
    
    if phase == 'train':
      model_with_loss.train()
    else:
      if len(self.opt.gpus) > 1:
        model_with_loss = self.model_with_loss.module
      model_with_loss.eval()
      torch.cuda.empty_cache()

    opt = self.opt
    results = {}
    data_time, batch_time = AverageMeter(), AverageMeter()
    avg_loss_stats = {l: AverageMeter() for l in self.loss_stats}
    num_iters = len(data_loader) if opt.num_iters < 0 else opt.num_iters
    bar = Bar('{}/{}'.format(opt.task, opt.exp_id), max=num_iters)
    end = time.time()
    
    for iter_id, batch in enumerate(data_loader):
      if iter_id >= num_iters:
        break
      data_time.update(time.time() - end)

      # DataLoader 输出调试：打印 batch 的 keys 和 shapes
      # if iter_id % 50 == 0 or iter_id < 5:  # 前5个batch + 每50个batch打印一次
      #   print(f"\n[DataLoader Debug] Batch {iter_id} - Phase: {phase}")
      #   print(f"[DataLoader Debug] Batch keys: {list(batch.keys())}")
      #
      #   for k, v in batch.items():
      #     if k != 'meta':
      #       if hasattr(v, 'shape'):
      #         print(f"[DataLoader Debug] {k}: shape={v.shape}, dtype={v.dtype}")
      #       else:
      #         print(f"[DataLoader Debug] {k}: type={type(v)}")
      #     else:
      #       print(f"[DataLoader Debug] {k}: meta info (keys: {list(v.keys()) if isinstance(v, dict) else 'not dict'})")
      #
      #   print("[DataLoader Debug] " + "-"*50)

      for k in batch:
        if k != 'meta':
          batch[k] = batch[k].to(device=opt.device, non_blocking=True)

      # 性能监控：前向传播计时
      if iter_id % 100 == 0:  # 每100个batch统计一次
        torch.cuda.synchronize()
        forward_start = time.time()

      output, loss, loss_stats = model_with_loss(epoch, batch)

      if iter_id % 100 == 0:
        torch.cuda.synchronize()
        forward_time = time.time() - forward_start
        print(f"[性能] Batch {iter_id} 前向传播耗时: {forward_time:.3f}秒")
        sync_start = time.time()

      loss = loss.mean()

      if iter_id % 100 == 0:
        torch.cuda.synchronize()
        sync_time = time.time() - sync_start
        print(f"[性能] Batch {iter_id} loss梯度聚合耗时: {sync_time:.3f}秒")

      if phase == 'train':
        # 性能监控：反向传播计时
        if iter_id % 100 == 0:
          torch.cuda.synchronize()
          backward_start = time.time()

        self.optimizer.zero_grad()
        loss.backward()

        if iter_id % 100 == 0:
          torch.cuda.synchronize()
          backward_time = time.time() - backward_start
          print(f"[性能] Batch {iter_id} 反向传播耗时: {backward_time:.3f}秒")

          # 性能监控：优化器更新计时
          torch.cuda.synchronize()
          optimizer_start = time.time()

        self.optimizer.step()

        if iter_id % 100 == 0:
          torch.cuda.synchronize()
          optimizer_time = time.time() - optimizer_start
          print(f"[性能] Batch {iter_id} 优化器更新耗时: {optimizer_time:.3f}秒")
      batch_time.update(time.time() - end)
      end = time.time()

      Bar.suffix = '{phase}: [{0}][{1}/{2}]|Tot: {total:} |ETA: {eta:} '.format(
        epoch, iter_id, num_iters, phase=phase,
        total=bar.elapsed_td, eta=bar.eta_td)
      for l in avg_loss_stats:
        avg_loss_stats[l].update(
          loss_stats[l].mean().item(), batch['input'].size(0))
        if l == 'loss_mj' or l == 'loss_mn' or l == 'smo_loss':
          Bar.suffix = Bar.suffix + '|{} {:.2f} '.format(l, avg_loss_stats[l].avg)
        else:
          Bar.suffix = Bar.suffix + '|{} {:.4f} '.format(l, avg_loss_stats[l].avg)
       
      if opt.print_iter > 0:
        if iter_id % opt.print_iter == 0:
          print('{}/{}| {}'.format(opt.task, opt.exp_id, Bar.suffix)) 
      else:
        bar.next()
      
      if opt.debug > 0:
        self.debug(batch, output, iter_id)
      
      if opt.test:
        self.save_result(output, batch, results)
      del output, loss, loss_stats
    
    bar.finish()
    ret = {k: v.avg for k, v in avg_loss_stats.items()}
    ret['time'] = bar.elapsed_td.total_seconds() / 60.
   
    return ret, results
  
  def debug(self, batch, output, iter_id):
    raise NotImplementedError

  def save_result(self, output, batch, results):
    raise NotImplementedError

  def _get_losses(self, opt):
    raise NotImplementedError
  
  def val(self, epoch, data_loader):
    return self.run_epoch('val', epoch, data_loader)

  def train(self, epoch, data_loader):
    return self.run_epoch('train', epoch, data_loader)

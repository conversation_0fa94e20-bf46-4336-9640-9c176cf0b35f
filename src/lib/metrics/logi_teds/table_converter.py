import json


def logi_json_to_html(json_file_path: str, debug: bool = False) -> str:
    """将JSON标注文件转换为HTML格式"""
    if debug:
        print(f"[DEBUG] Converting JSON file: {json_file_path}")

    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"[ERROR] Failed to read JSON file {json_file_path}: {e}")
        return '<table></table>'

    cells = data.get('cells', [])
    if not cells:
        if debug:
            print(f"[DEBUG] No cells found in JSON file")
        return '<html><body><table></table></body></html>'

    if debug:
        print(f"[DEBUG] Found {len(cells)} cells in JSON")

    # 构建表格结构：找出最大行列数
    max_row = 0
    max_col = 0
    invalid_cells = 0

    for i, cell in enumerate(cells):
        lloc = cell.get('lloc', {})
        if not lloc:
            if debug:
                print(f"[DEBUG] Cell {i} has no lloc information")
            invalid_cells += 1
            continue

        start_row = lloc.get('start_row', 0)
        end_row = lloc.get('end_row', 0)
        start_col = lloc.get('start_col', 0)
        end_col = lloc.get('end_col', 0)

        # 验证坐标有效性
        if (start_row < 0 or end_row < 0 or start_col < 0 or end_col < 0 or
            start_row > end_row or start_col > end_col):
            if debug:
                print(f"[DEBUG] Cell {i} has invalid coordinates: start_row={start_row}, end_row={end_row}, start_col={start_col}, end_col={end_col}")
            invalid_cells += 1
            continue

        max_row = max(max_row, end_row)
        max_col = max(max_col, end_col)

    if debug:
        print(f"[DEBUG] Table dimensions: {max_row + 1} rows x {max_col + 1} cols")
        if invalid_cells > 0:
            print(f"[DEBUG] Found {invalid_cells} invalid cells")

    # 创建表格矩阵，初始化为None
    table_matrix = [[None for _ in range(max_col + 1)] for _ in range(max_row + 1)]

    # 填充单元格信息
    for cell in cells:
        lloc = cell.get('lloc', {})
        start_row = lloc.get('start_row', 0)
        end_row = lloc.get('end_row', 0)
        start_col = lloc.get('start_col', 0)
        end_col = lloc.get('end_col', 0)

        # 计算跨行跨列
        rowspan = end_row - start_row + 1
        colspan = end_col - start_col + 1

        # 创建单元格信息
        cell_info = {
            'text': '',
            'rowspan': rowspan if rowspan > 1 else None,
            'colspan': colspan if colspan > 1 else None
        }

        # 在起始位置放置单元格信息
        table_matrix[start_row][start_col] = cell_info

        # 在跨行跨列的其他位置标记为占位
        for r in range(start_row, end_row + 1):
            for c in range(start_col, end_col + 1):
                if r != start_row or c != start_col:
                    table_matrix[r][c] = 'OCCUPIED'

    # 生成HTML
    html_parts = ['<html><body><table>']

    for row in table_matrix:
        html_parts.append('<tr>')
        for cell in row:
            if cell is None:
                html_parts.append('<td></td>')
            elif cell == 'OCCUPIED':
                continue
            else:
                attrs = []
                if cell['rowspan']:
                    attrs.append(f'rowspan="{cell["rowspan"]}"')
                if cell['colspan']:
                    attrs.append(f'colspan="{cell["colspan"]}"')

                attr_str = ' ' + ' '.join(attrs) if attrs else ''
                html_parts.append(f'<td{attr_str}>{cell["text"]}</td>')

        html_parts.append('</tr>')

    html_parts.append('</table></body></html>')

    html_result = ''.join(html_parts)

    if debug:
        print(f"[DEBUG] Generated HTML length: {len(html_result)}")
        print(f"[DEBUG] HTML preview: {html_result[:200]}...")

    return html_result


def logi_txt_to_html(txt_file_path: str, debug: bool = False) -> str:
    """将预测txt文件转换为HTML格式，使用表格矩阵方法确保单元格数量一致"""
    if debug:
        print(f"[DEBUG] Converting prediction file: {txt_file_path}")

    try:
        with open(txt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"[ERROR] Failed to read prediction file {txt_file_path}: {e}")
        return '<html><body><table></table></body></html>'

    # 解析TXT文件中的所有单元格
    txt_cells = []
    for line in lines:
        line = line.strip()
        if not line:
            continue

        try:
            parts = line.split(',')
            if len(parts) >= 4:
                start_row = int(parts[0])
                end_row = int(parts[1])
                start_col = int(parts[2])
                end_col = int(parts[3])

                # 验证坐标有效性
                if (start_row >= 0 and end_row >= 0 and start_col >= 0 and end_col >= 0 and
                    start_row <= end_row and start_col <= end_col):
                    txt_cells.append({
                        'start_row': start_row,
                        'end_row': end_row,
                        'start_col': start_col,
                        'end_col': end_col
                    })
        except ValueError:
            continue

    if debug:
        print(f"[DEBUG] Parsed {len(txt_cells)} valid cells from TXT file")

    if not txt_cells:
        return '<html><body><table></table></body></html>'

    # 去除重复的单元格定义
    unique_cells = []
    seen_cells = set()
    duplicates_removed = 0

    for cell in txt_cells:
        # 创建单元格的唯一标识
        cell_key = (cell['start_row'], cell['end_row'], cell['start_col'], cell['end_col'])

        if cell_key not in seen_cells:
            seen_cells.add(cell_key)
            unique_cells.append(cell)
        else:
            duplicates_removed += 1

    if debug:
        print(f"[DEBUG] Removed {duplicates_removed} duplicate cells")
        print(f"[DEBUG] Unique cells: {len(unique_cells)}")

    # 使用去重后的单元格列表
    txt_cells = unique_cells

    # 应用通用转置算法：将TXT坐标转置为正确的逻辑结构
    if debug:
        print(f"[DEBUG] Applying universal transpose algorithm to {len(txt_cells)} cells")

    # 分析原始坐标范围
    original_max_row = max(cell['end_row'] for cell in txt_cells)
    original_max_col = max(cell['end_col'] for cell in txt_cells)

    if debug:
        print(f"[DEBUG] Original dimensions: {original_max_row + 1} rows x {original_max_col + 1} cols")

    # 应用坐标转置：(start_row, end_row, start_col, end_col) -> (start_col, end_col, start_row, end_row)
    transposed_cells = []

    for cell in txt_cells:
        # 转置坐标
        new_start_row = cell['start_col']
        new_end_row = cell['end_col']
        new_start_col = cell['start_row']
        new_end_col = cell['end_row']

        # 验证转置后的坐标有效性
        if (new_start_row >= 0 and new_end_row >= 0 and
            new_start_col >= 0 and new_end_col >= 0 and
            new_start_row <= new_end_row and new_start_col <= new_end_col):

            transposed_cells.append({
                'start_row': new_start_row,
                'end_row': new_end_row,
                'start_col': new_start_col,
                'end_col': new_end_col,
                'text': ''
            })

    if debug:
        transposed_max_row = max(cell['end_row'] for cell in transposed_cells) if transposed_cells else 0
        transposed_max_col = max(cell['end_col'] for cell in transposed_cells) if transposed_cells else 0
        print(f"[DEBUG] Transposed dimensions: {transposed_max_row + 1} rows x {transposed_max_col + 1} cols")
        print(f"[DEBUG] Successfully transposed {len(transposed_cells)}/{len(txt_cells)} cells")

    # 使用表格矩阵方法转换为HTML（与json_to_html相同的方法）
    if not transposed_cells:
        return '<html><body><table></table></body></html>'

    # 构建表格结构：找出最大行列数
    max_row = 0
    max_col = 0
    invalid_cells = 0

    for i, cell in enumerate(transposed_cells):
        start_row = cell['start_row']
        end_row = cell['end_row']
        start_col = cell['start_col']
        end_col = cell['end_col']

        # 验证坐标有效性（已经在转置时验证过，但再次确认）
        if (start_row < 0 or end_row < 0 or start_col < 0 or end_col < 0 or
            start_row > end_row or start_col > end_col):
            if debug:
                print(f"[DEBUG] Cell {i} has invalid coordinates: start_row={start_row}, end_row={end_row}, start_col={start_col}, end_col={end_col}")
            invalid_cells += 1
            continue

        max_row = max(max_row, end_row)
        max_col = max(max_col, end_col)

    if debug:
        print(f"[DEBUG] Table dimensions: {max_row + 1} rows x {max_col + 1} cols")
        if invalid_cells > 0:
            print(f"[DEBUG] Found {invalid_cells} invalid cells")

    # 创建表格矩阵，初始化为None
    table_matrix = [[None for _ in range(max_col + 1)] for _ in range(max_row + 1)]

    # 填充单元格信息
    for cell in transposed_cells:
        start_row = cell['start_row']
        end_row = cell['end_row']
        start_col = cell['start_col']
        end_col = cell['end_col']

        # 计算跨行跨列
        rowspan = end_row - start_row + 1
        colspan = end_col - start_col + 1

        # 创建单元格信息
        cell_info = {
            'text': cell.get('text', ''),
            'rowspan': rowspan if rowspan > 1 else None,
            'colspan': colspan if colspan > 1 else None
        }

        # 在起始位置放置单元格信息
        table_matrix[start_row][start_col] = cell_info

        # 在跨行跨列的其他位置标记为占位
        for r in range(start_row, end_row + 1):
            for c in range(start_col, end_col + 1):
                if r != start_row or c != start_col:
                    table_matrix[r][c] = 'OCCUPIED'

    # 生成HTML - 使用表格矩阵方法但不生成多余的空单元格
    html_parts = ['<html><body><table>']

    for row in table_matrix:
        html_parts.append('<tr>')
        for cell in row:
            if cell is None:
                # 跳过空位置，不生成<td></td>
                continue
            elif cell == 'OCCUPIED':
                # 跳过被占用的位置
                continue
            else:
                # 生成实际的单元格
                attrs = []
                if cell['rowspan']:
                    attrs.append(f'rowspan="{cell["rowspan"]}"')
                if cell['colspan']:
                    attrs.append(f'colspan="{cell["colspan"]}"')

                attr_str = ' ' + ' '.join(attrs) if attrs else ''
                html_parts.append(f'<td{attr_str}>{cell["text"]}</td>')

        html_parts.append('</tr>')

    html_parts.append('</table></body></html>')

    html_result = ''.join(html_parts)

    if debug:
        print(f"[DEBUG] Generated HTML length: {len(html_result)}")
        print(f"[DEBUG] HTML preview: {html_result[:200]}...")
        print(f"[DEBUG] Input cells: {len(txt_cells)}, Output cells in HTML: {html_result.count('<td')}")

    return html_result


def logi_my_txt_to_html(txt_file_path: str, debug: bool = False) -> str:
    """将预测txt文件转换为HTML格式，使用表格矩阵方法确保单元格数量一致"""
    if debug:
        print(f"[DEBUG] Converting prediction file: {txt_file_path}")

    try:
        with open(txt_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"[ERROR] Failed to read prediction file {txt_file_path}: {e}")
        return '<html><body><table></table></body></html>'

    # 解析TXT文件中的所有单元格
    txt_cells = []
    for line in lines:
        line = line.strip()
        if not line:
            continue

        try:
            parts = line.split(',')
            if len(parts) >= 4:
                start_row = int(parts[0])
                end_row = int(parts[1])
                start_col = int(parts[2])
                end_col = int(parts[3])

                # 验证坐标有效性
                if (start_row >= 0 and end_row >= 0 and start_col >= 0 and end_col >= 0 and
                    start_row <= end_row and start_col <= end_col):
                    txt_cells.append({
                        'start_row': start_row,
                        'end_row': end_row,
                        'start_col': start_col,
                        'end_col': end_col
                    })
        except ValueError:
            continue

    if debug:
        print(f"[DEBUG] Parsed {len(txt_cells)} valid cells from TXT file")

    if not txt_cells:
        return '<html><body><table></table></body></html>'

    # 去除重复的单元格定义
    unique_cells = []
    seen_cells = set()
    duplicates_removed = 0

    for cell in txt_cells:
        # 创建单元格的唯一标识
        cell_key = (cell['start_row'], cell['end_row'], cell['start_col'], cell['end_col'])

        if cell_key not in seen_cells:
            seen_cells.add(cell_key)
            unique_cells.append(cell)
        else:
            duplicates_removed += 1

    if debug:
        print(f"[DEBUG] Removed {duplicates_removed} duplicate cells")
        print(f"[DEBUG] Unique cells: {len(unique_cells)}")

    # 使用去重后的单元格列表
    txt_cells = unique_cells

    # 应用通用转置算法：将TXT坐标转置为正确的逻辑结构
    if debug:
        print(f"[DEBUG] Applying universal transpose algorithm to {len(txt_cells)} cells")

    # 分析原始坐标范围
    original_max_row = max(cell['end_row'] for cell in txt_cells)
    original_max_col = max(cell['end_col'] for cell in txt_cells)

    if debug:
        print(f"[DEBUG] Original dimensions: {original_max_row + 1} rows x {original_max_col + 1} cols")

    # 应用坐标转置：(start_row, end_row, start_col, end_col) -> (start_col, end_col, start_row, end_row)
    transposed_cells = txt_cells

    if debug:
        transposed_max_row = max(cell['end_row'] for cell in transposed_cells) if transposed_cells else 0
        transposed_max_col = max(cell['end_col'] for cell in transposed_cells) if transposed_cells else 0
        print(f"[DEBUG] Transposed dimensions: {transposed_max_row + 1} rows x {transposed_max_col + 1} cols")
        print(f"[DEBUG] Successfully transposed {len(transposed_cells)}/{len(txt_cells)} cells")

    # 使用表格矩阵方法转换为HTML（与json_to_html相同的方法）
    if not transposed_cells:
        return '<html><body><table></table></body></html>'

    # 构建表格结构：找出最大行列数
    max_row = 0
    max_col = 0
    invalid_cells = 0

    for i, cell in enumerate(transposed_cells):
        start_row = cell['start_row']
        end_row = cell['end_row']
        start_col = cell['start_col']
        end_col = cell['end_col']

        # 验证坐标有效性（已经在转置时验证过，但再次确认）
        if (start_row < 0 or end_row < 0 or start_col < 0 or end_col < 0 or
            start_row > end_row or start_col > end_col):
            if debug:
                print(f"[DEBUG] Cell {i} has invalid coordinates: start_row={start_row}, end_row={end_row}, start_col={start_col}, end_col={end_col}")
            invalid_cells += 1
            continue

        max_row = max(max_row, end_row)
        max_col = max(max_col, end_col)

    if debug:
        print(f"[DEBUG] Table dimensions: {max_row + 1} rows x {max_col + 1} cols")
        if invalid_cells > 0:
            print(f"[DEBUG] Found {invalid_cells} invalid cells")

    # 创建表格矩阵，初始化为None
    table_matrix = [[None for _ in range(max_col + 1)] for _ in range(max_row + 1)]

    # 填充单元格信息
    for cell in transposed_cells:
        start_row = cell['start_row']
        end_row = cell['end_row']
        start_col = cell['start_col']
        end_col = cell['end_col']

        # 计算跨行跨列
        rowspan = end_row - start_row + 1
        colspan = end_col - start_col + 1

        # 创建单元格信息
        cell_info = {
            'text': cell.get('text', ''),
            'rowspan': rowspan if rowspan > 1 else None,
            'colspan': colspan if colspan > 1 else None
        }

        # 在起始位置放置单元格信息
        table_matrix[start_row][start_col] = cell_info

        # 在跨行跨列的其他位置标记为占位
        for r in range(start_row, end_row + 1):
            for c in range(start_col, end_col + 1):
                if r != start_row or c != start_col:
                    table_matrix[r][c] = 'OCCUPIED'

    # 生成HTML - 使用表格矩阵方法但不生成多余的空单元格
    html_parts = ['<html><body><table>']

    for row in table_matrix:
        html_parts.append('<tr>')
        for cell in row:
            if cell is None:
                # 跳过空位置，不生成<td></td>
                continue
            elif cell == 'OCCUPIED':
                # 跳过被占用的位置
                continue
            else:
                # 生成实际的单元格
                attrs = []
                if cell['rowspan']:
                    attrs.append(f'rowspan="{cell["rowspan"]}"')
                if cell['colspan']:
                    attrs.append(f'colspan="{cell["colspan"]}"')

                attr_str = ' ' + ' '.join(attrs) if attrs else ''
                html_parts.append(f'<td{attr_str}>{cell["text"]}</td>')

        html_parts.append('</tr>')

    html_parts.append('</table></body></html>')

    html_result = ''.join(html_parts)

    if debug:
        print(f"[DEBUG] Generated HTML length: {len(html_result)}")
        print(f"[DEBUG] HTML preview: {html_result[:200]}...")
        print(f"[DEBUG] Input cells: {len(txt_cells)}, Output cells in HTML: {html_result.count('<td')}")

    return html_result

"""
邻接关系评估工具

本模块用于评估表格结构识别中的邻接关系准确性。主要功能包括：

1. 解析Ground Truth JSON文件，提取单元格逻辑位置信息
2. 解析预测结果TXT文件，提取单元格坐标信息
3. 计算单元格间的邻接关系（水平和垂直）
4. 评估预测结果与真实标注的邻接关系匹配度
5. 批量处理目录中的所有匹配文件对
6. 计算precision、recall、f1-score等评估指标

使用示例:
    from adjacency_evaluator import evaluate_adjacency_metrics

    result = evaluate_adjacency_metrics(
        gt_dir="path/to/ground_truth_annos",
        pred_dir="path/to/preds_logis",
        transpose=False,
        verbose=True
    )

    print(f"平均F1分数: {result['f1_score']:.4f}")

文件格式要求:
- GT文件: *_table_annotation.json，包含cells数组和lloc字段
- 预测文件: *.jpg.txt或*.png.txt，每行4个逗号分隔的数字

作者: LORE-TSR项目组
日期: 2025-01-31
"""

import pprint
import json
import os
import glob
import sys
from typing import List, Dict, Tuple, Optional

def extract_logi_txt_file(file_path: str, transpose: bool = False) -> List[Dict]:
    """
    从逻辑txt文件中提取单元格信息， 模型预测结果，样例见 @533771053059540074.jpg.txt

    Args:
        file_path: TXT文件路径
        transpose: 是否转置解析，False表示 start_row,end_row,start_col,end_col
                  True表示 start_col,end_col,start_row,end_row

    Returns:
        单元格列表，每个单元格包含 start_row, end_row, start_col, end_col

    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 数据格式错误
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    cells = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                # 解析逗号分隔的数据
                parts = line.split(',')
                if len(parts) != 4:
                    print(f"警告: 第{line_num}行格式错误，期望4个数字，实际{len(parts)}个: {line}")
                    continue

                try:
                    values = [int(part.strip()) for part in parts]
                except ValueError as e:
                    print(f"警告: 第{line_num}行数字解析错误: {line}, 错误: {e}")
                    continue

                # 根据transpose参数决定解析顺序
                if transpose:
                    # start_col, end_col, start_row, end_row
                    start_col, end_col, start_row, end_row = values
                else:
                    # start_row, end_row, start_col, end_col
                    start_row, end_row, start_col, end_col = values

                cell_info = {
                    'start_row': start_row,
                    'end_row': end_row,
                    'start_col': start_col,
                    'end_col': end_col
                }
                cells.append(cell_info)

    except Exception as e:
        raise ValueError(f"读取文件错误 {file_path}: {e}")

    return cells

def extract_logi_json_file(file_path: str) -> List[Dict]:
    """
    从逻辑JSON文件中提取单元格信息, Ground Truth 标注，样例见 @533771053059540074_table_annotation.json

    Args:
        file_path: JSON文件路径

    Returns:
        单元格列表，每个单元格包含 start_row, end_row, start_col, end_col

    Raises:
        FileNotFoundError: 文件不存在
        json.JSONDecodeError: JSON格式错误
        KeyError: 缺少必要字段
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"JSON解析错误 {file_path}: {e}")

    if 'cells' not in data:
        raise KeyError(f"JSON文件缺少'cells'字段: {file_path}")

    cells = []
    for cell in data['cells']:
        if 'lloc' not in cell:
            continue  # 跳过没有逻辑位置信息的单元格

        lloc = cell['lloc']
        required_fields = ['start_row', 'end_row', 'start_col', 'end_col']

        # 检查必要字段
        if not all(field in lloc for field in required_fields):
            continue  # 跳过缺少字段的单元格

        cell_info = {
            'start_row': lloc['start_row'],
            'end_row': lloc['end_row'],
            'start_col': lloc['start_col'],
            'end_col': lloc['end_col']
        }
        cells.append(cell_info)

    return cells


def find_matching_files(gt_dir: str, pred_dir: str) -> List[Tuple[str, str]]:
    """
    在两个目录中找到匹配的文件对

    Args:
        gt_dir: Ground Truth目录，包含*_table_annotation.json文件
        pred_dir: 预测结果目录，包含*.jpg.txt或*.png.txt文件

    Returns:
        匹配的文件对列表 [(gt_file_path, pred_file_path), ...]
    """
    if not os.path.exists(gt_dir):
        raise FileNotFoundError(f"Ground Truth目录不存在: {gt_dir}")
    if not os.path.exists(pred_dir):
        raise FileNotFoundError(f"预测结果目录不存在: {pred_dir}")

    matching_pairs = []

    # 获取所有GT JSON文件
    gt_pattern = os.path.join(gt_dir, "*_table_annotation.json")
    gt_files = glob.glob(gt_pattern)

    for gt_file in gt_files:
        # 从GT文件名提取前缀
        gt_basename = os.path.basename(gt_file)
        # 移除 "_table_annotation.json" 后缀
        if gt_basename.endswith("_table_annotation.json"):
            prefix = gt_basename[:-len("_table_annotation.json")]
        else:
            continue  # 跳过不符合命名规范的文件

        # 查找对应的预测文件
        pred_candidates = [
            os.path.join(pred_dir, f"{prefix}.jpg.txt"),
            os.path.join(pred_dir, f"{prefix}.png.txt")
        ]

        pred_file = None
        for candidate in pred_candidates:
            if os.path.exists(candidate):
                pred_file = candidate
                break

        if pred_file:
            matching_pairs.append((gt_file, pred_file))
        else:
            print(f"警告: 未找到与 {gt_basename} 匹配的预测文件")

    return matching_pairs


def get_adjacency_relations_robust(cells):
    """
    从单元格列表中提取所有水平和垂直的邻接关系（鲁棒版本，支持跨行跨列）。

    Args:
        cells (list): 单元格列表。

    Returns:
        set: 邻接关系集合。
    """
    relations = set()

    # 使用起始坐标作为唯一ID
    cells_by_id = {(cell['start_row'], cell['start_col']): cell for cell in cells}
    cell_ids = list(cells_by_id.keys())

    for i in range(len(cell_ids)):
        for j in range(len(cell_ids)):
            if i == j:
                continue

            cell_1_id = cell_ids[i]
            cell_2_id = cell_ids[j]

            cell_1 = cells_by_id[cell_1_id]
            cell_2 = cells_by_id[cell_2_id]

            # 检查水平邻接关系 (cell_2 在 cell_1 的右边)
            if cell_1['end_col'] + 1 == cell_2['start_col']:
                # 检查行范围是否有重叠
                if max(cell_1['start_row'], cell_2['start_row']) <= min(cell_1['end_row'], cell_2['end_row']):
                    relations.add((cell_1_id, cell_2_id, 'horizontal'))

            # 检查垂直邻接关系 (cell_2 在 cell_1 的下方)
            if cell_1['end_row'] + 1 == cell_2['start_row']:
                # 检查列范围是否有重叠
                if max(cell_1['start_col'], cell_2['start_col']) <= min(cell_1['end_col'], cell_2['end_col']):
                    relations.add((cell_1_id, cell_2_id, 'vertical'))

    return relations


def calculate_adjacency_metrics(gt_cells, pred_cells, verbose=True):
    """
    计算基于邻接关系的 Precision, Recall, F1-Score。
    """
    gt_relations = get_adjacency_relations_robust(gt_cells)
    pred_relations = get_adjacency_relations_robust(pred_cells)

    if verbose:
        print("--- Ground-Truth Relations ---")
        pprint.pprint(sorted(list(gt_relations)))

        print("\n--- Predicted Relations ---")
        pprint.pprint(sorted(list(pred_relations)))

    tp = len(gt_relations.intersection(pred_relations))
    fp = len(pred_relations - gt_relations)
    fn = len(gt_relations - pred_relations)

    if verbose:
        print(f"\nTP: {tp}, FP: {fp}, FN: {fn}")

    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

    return {
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score
    }


def evaluate_adjacency_metrics(gt_dir: str, pred_dir: str, transpose: bool = False, verbose: bool = True) -> Dict[str, float]:
    """
    评估两个目录中所有匹配文件对的邻接关系指标

    Args:
        gt_dir: Ground Truth目录路径
        pred_dir: 预测结果目录路径
        transpose: 是否转置解析预测文件坐标顺序
        verbose: 是否打印详细信息

    Returns:
        包含平均precision、recall、f1_score的字典

    Raises:
        FileNotFoundError: 目录不存在
        ValueError: 没有找到匹配的文件对
    """
    print(f"开始评估邻接关系指标...")
    print(f"Ground Truth目录: {gt_dir}")
    print(f"预测结果目录: {pred_dir}")
    print(f"转置解析: {transpose}")

    # 找到匹配的文件对
    matching_pairs = find_matching_files(gt_dir, pred_dir)

    if not matching_pairs:
        raise ValueError(f"未找到匹配的文件对，请检查目录路径和文件命名")

    print(f"找到 {len(matching_pairs)} 对匹配文件")

    all_metrics = []
    successful_pairs = 0
    failed_pairs = 0

    for i, (gt_file, pred_file) in enumerate(matching_pairs, 1):
        try:
            if verbose:
                print(f"\n--- 处理第 {i}/{len(matching_pairs)} 对文件 ---")
                print(f"GT文件: {os.path.basename(gt_file)}")
                print(f"预测文件: {os.path.basename(pred_file)}")

            # 提取单元格信息
            gt_cells = extract_logi_json_file(gt_file)
            pred_cells = extract_logi_txt_file(pred_file, transpose=transpose)

            if verbose:
                print(f"GT单元格数量: {len(gt_cells)}")
                print(f"预测单元格数量: {len(pred_cells)}")

            # 计算指标
            metrics = calculate_adjacency_metrics(gt_cells, pred_cells, verbose=verbose)
            all_metrics.append(metrics)
            successful_pairs += 1

            if verbose:
                print(f"指标结果: Precision={metrics['precision']:.4f}, "
                      f"Recall={metrics['recall']:.4f}, F1={metrics['f1_score']:.4f}")

        except Exception as e:
            failed_pairs += 1
            print(f"错误: 处理文件对失败 - {e}")
            if verbose:
                print(f"GT文件: {gt_file}")
                print(f"预测文件: {pred_file}")
            continue

    if not all_metrics:
        raise ValueError("没有成功处理任何文件对")

    # 计算平均指标
    avg_precision = sum(m['precision'] for m in all_metrics) / len(all_metrics)
    avg_recall = sum(m['recall'] for m in all_metrics) / len(all_metrics)
    avg_f1_score = sum(m['f1_score'] for m in all_metrics) / len(all_metrics)

    result = {
        'precision': avg_precision,
        'recall': avg_recall,
        'f1_score': avg_f1_score,
        'total_pairs': len(matching_pairs),
        'successful_pairs': successful_pairs,
        'failed_pairs': failed_pairs
    }

    print(f"\n=== 最终评估结果 ===")
    print(f"总文件对数: {result['total_pairs']}")
    print(f"成功处理: {result['successful_pairs']}")
    print(f"处理失败: {result['failed_pairs']}")
    print(f"平均 Precision: {result['precision']:.4f}")
    print(f"平均 Recall: {result['recall']:.4f}")
    print(f"平均 F1-Score: {result['f1_score']:.4f}")

    return result


def main_usage_example():
    """
    主要使用示例函数

    演示如何使用evaluate_adjacency_metrics函数进行批量评估
    """
    print("=== 邻接关系评估工具使用示例 ===")

    # 示例目录路径
    gt_dir = "path/to/ground_truth_annos"  # 包含*_table_annotation.json文件的目录
    pred_dir = "path/to/preds_logis"       # 包含*.jpg.txt或*.png.txt文件的目录

    print(f"""
使用方法:
1. 准备Ground Truth目录: {gt_dir}
   - 包含以'_table_annotation.json'结尾的JSON文件
   - 每个JSON文件包含'cells'数组，每个cell有'lloc'字段

2. 准备预测结果目录: {pred_dir}
   - 包含以'.jpg.txt'或'.png.txt'结尾的TXT文件
   - 每行包含4个逗号分隔的数字

3. 调用评估函数:
   result = evaluate_adjacency_metrics(gt_dir, pred_dir, transpose=False)

4. 参数说明:
   - gt_dir: Ground Truth目录路径
   - pred_dir: 预测结果目录路径
   - transpose: 是否转置解析预测文件坐标
     * False: start_row,end_row,start_col,end_col (默认)
     * True:  start_col,end_col,start_row,end_row
   - verbose: 是否显示详细处理信息

5. 返回结果:
   - precision: 平均精确率
   - recall: 平均召回率
   - f1_score: 平均F1分数
   - total_pairs: 总文件对数
   - successful_pairs: 成功处理的文件对数
   - failed_pairs: 处理失败的文件对数
""")

    # 使用样例数据进行演示
    try:
        sample_gt_dir = "src/lib/metrics/eval_adjacency/samples/gt"
        sample_pred_dir = "src/lib/metrics/eval_adjacency/samples/pred"

        print("使用样例数据进行演示:")
        result = evaluate_adjacency_metrics(
            sample_gt_dir,
            sample_pred_dir,
            transpose=False,
            verbose=False
        )

        print("\n演示完成！")

    except Exception as e:
        print(f"演示运行失败: {e}")


if __name__ == '__main__':

    gt_dir = '/aipdf-mlp/lanx/workspace/datasets/wired_optimizer/annos'
    # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark_official/logi'
    # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark/logi'
    pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark_epoch20/logi'

    # gt_dir = '/aipdf-mlp/lanx/workspace/datasets/wireless_optimizer/annos'
    # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wireless_benchmark_official/logi'
    # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wireless_benchmark/logi'

    try:
        # 执行评估
        result = evaluate_adjacency_metrics(
            gt_dir=gt_dir,
            pred_dir=pred_dir,
            transpose=False,
        )

        # 输出结果摘要
        print(f"\n{'=' * 50}")
        print(f"邻接关系评估完成!")
        print(f"{'=' * 50}")
        print(f"处理文件对: {result['successful_pairs']}/{result['total_pairs']}")
        print(f"平均精确率 (Precision): {result['precision']:.4f}")
        print(f"平均召回率 (Recall): {result['recall']:.4f}")
        print(f"平均F1分数 (F1-Score): {result['f1_score']:.4f}")

        if result['failed_pairs'] > 0:
            print(f"处理失败: {result['failed_pairs']} 个文件对")

    except Exception as e:
        print(f"评估过程中发生错误: {e}")
        sys.exit(1)
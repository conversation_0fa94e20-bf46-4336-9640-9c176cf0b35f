# 表格结构分析系统设计文档

## 概述

本文档详细描述了 TableLabelMe 项目中表格结构分析系统的设计与实现。该系统负责将用户标注的物理单元格转换为逻辑表格矩阵，支持智能容差修正和合并单元格检测。

## 系统架构

### 核心组件

```mermaid
graph TD
    A[app_tableme.py] --> B[TableController]
    B --> C[TableAnalyzer]
    C --> D[智能分析模式]
    C --> E[精确分析模式]
    D --> F[边界检测与修正]
    E --> F
    F --> G[逻辑网格构建]
    G --> H[结果应用]
    H --> I[TableStructureWidget]
    
    J[AlignmentToolWidget] --> K[容差控制]
    K --> C
```

### 主要文件结构

- `labelme/app_tableme.py` - 主应用入口，用户交互界面
- `labelme/utils/table_analyzer.py` - 核心分析引擎
- `labelme/widgets/table_controller.py` - 表格控制器，管理单元格
- `labelme/widgets/table_structure_widget.py` - 逻辑结构视图组件

## 详细调用链分析

### 1. 入口点：用户触发分析

**文件**: `labelme/app_tableme.py`
**方法**: `analyze_table_structure()`

```python
def analyze_table_structure(self):
    """分析表格结构"""
    active_controller = self.multi_table_controller.get_active_controller()
    if not active_controller:
        self.statusBar().showMessage("请先创建表格或绘制单元格")
        return

    try:
        # 获取当前表格的所有单元格
        cells = active_controller.get_table_cells()
        if not cells:
            self.statusBar().showMessage("当前表格没有单元格")
            return

        # 分析表格结构
        result = TableAnalyzer.analyze_cells_to_grid(cells)
        TableAnalyzer.print_grid_analysis(result)
        # 应用分析结果到逻辑坐标
        active_controller._apply_analysis_result(result)

        rows = result['grid']['rows']
        cols = result['grid']['cols']
        self.statusBar().showMessage(f"表格结构分析完成: {rows}行 × {cols}列")

    except Exception as e:
        QtWidgets.QMessageBox.critical(self, "分析失败", f"表格结构分析失败:\n{e}")
```

**触发条件**:

- 用户点击"分析表格结构"按钮
- 快捷键 `A`
- 其他操作后的自动分析（如对齐、拆分等）

### 2. 核心分析引擎

**文件**: `labelme/utils/table_analyzer.py`
**方法**: `TableAnalyzer.analyze_cells_to_grid()`

```python
@staticmethod
def analyze_cells_to_grid(cells: List[TableCellShape]) -> Dict[str, Any]:
    """将单元格列表分析成逻辑表格网格
    
    返回格式:
    {
        'grid': {
            'rows': int,           # 总行数
            'cols': int,           # 总列数
            'matrix': list[list],  # 逻辑矩阵 matrix[row][col] = cell_id
        },
        'cell_positions': {
            cell_id: {
                'row': int,        # 逻辑行索引
                'col': int,        # 逻辑列索引
                'row_span': int,   # 跨行数
                'col_span': int,   # 跨列数
                'is_merged': bool, # 是否合并单元格
                'physical_bounds': (x1, y1, x2, y2),
                'cell_object': cell
            }
        },
        'boundaries': {
            'rows': [y1, y2, ...],    # 行边界坐标
            'cols': [x1, x2, ...]     # 列边界坐标
        }
    }
    """
    # 1. 输入验证
    if not cells or len(cells) < 1:
        raise ValueError("需要至少1个单元格")

    # 2. 根据设置选择分析模式
    if TableAnalyzer.enable_smart_analysis:
        return TableAnalyzer._analyze_with_smart_mode(cells)
    else:
        return TableAnalyzer._analyze_with_precise_mode(cells)
```

### 3. 智能分析模式

**核心特性**: 支持容差修正，自动处理边界不对齐问题

```python
@staticmethod
def _analyze_with_smart_mode(cells: List[TableCellShape]) -> Dict[str, Any]:
    """智能分析模式 - 带容差修正"""
    tolerance = TableAnalyzer.default_tolerance

    # 提取物理边界
    cell_bounds = TableAnalyzer._extract_physical_bounds(cells)

    # 检测边界问题
    boundary_issues = TableAnalyzer._detect_boundary_issues(cell_bounds, tolerance)

    # 如果没有问题，使用精确分析
    if not boundary_issues['has_issues']:
        return TableAnalyzer._analyze_with_precise_mode(cells)

    # 有问题时，使用容差修正
    raw_row_coords = [coord for _, y1, _, y2, _, _ in cell_bounds for coord in [y1, y2]]
    raw_col_coords = [coord for x1, _, x2, _, _, _ in cell_bounds for coord in [x1, x2]]

    corrected_row_boundaries = TableAnalyzer._merge_close_boundaries(raw_row_coords, tolerance)
    corrected_col_boundaries = TableAnalyzer._merge_close_boundaries(raw_col_coords, tolerance)

    # 构建修正后的逻辑网格
    corrected_grid = TableAnalyzer._build_logical_grid_smart(
        cell_bounds, corrected_row_boundaries, corrected_col_boundaries, tolerance
    )
    
    return result
```

### 4. 边界检测与修正算法

#### 4.1 边界问题检测

```python
@staticmethod
def _detect_boundary_issues(cell_bounds: List[Tuple], tolerance: float) -> Dict:
    """检测边界问题"""
    # 提取所有坐标
    row_coords = [coord for _, y1, _, y2, _, _ in cell_bounds for coord in [y1, y2]]
    col_coords = [coord for x1, _, x2, _, _, _ in cell_bounds for coord in [x1, x2]]

    # 检测相近边界
    row_issues = TableAnalyzer._find_close_coordinates(row_coords, tolerance)
    col_issues = TableAnalyzer._find_close_coordinates(col_coords, tolerance)

    has_issues = len(row_issues) > 0 or len(col_issues) > 0
    
    return {
        'has_issues': has_issues,
        'row_issues': row_issues,
        'col_issues': col_issues,
        'tolerance_used': tolerance
    }
```

#### 4.2 边界合并算法

```python
@staticmethod
def _merge_close_boundaries(coords: List[float], tolerance: float) -> List[float]:
    """合并相近边界"""
    if not coords:
        return []

    unique_coords = sorted(set(coords))
    if len(unique_coords) <= 1:
        return unique_coords

    merged = [unique_coords[0]]
    merge_count = 0

    for current in unique_coords[1:]:
        if abs(current - merged[-1]) <= tolerance:
            # 合并：取平均值
            old_boundary = merged[-1]
            merged[-1] = (merged[-1] + current) / 2.0
            merge_count += 1
        else:
            # 保留：距离足够大
            merged.append(current)

    return merged
```

### 5. 逻辑网格构建

```python
@staticmethod
def _build_logical_grid_smart(cell_bounds: List[Tuple], row_boundaries: List[float],
                              col_boundaries: List[float], tolerance: float) -> Dict:
    """构建逻辑网格 - 智能版本"""
    rows = len(row_boundaries) - 1
    cols = len(col_boundaries) - 1

    # 初始化矩阵
    matrix = [[None for _ in range(cols)] for _ in range(rows)]
    cell_positions = {}

    for x1, y1, x2, y2, cell_id, cell in cell_bounds:
        # 使用容差查找边界索引
        start_row = TableAnalyzer._find_boundary_index_smart(y1, row_boundaries, tolerance)
        end_row = TableAnalyzer._find_boundary_index_smart(y2, row_boundaries, tolerance)
        start_col = TableAnalyzer._find_boundary_index_smart(x1, col_boundaries, tolerance)
        end_col = TableAnalyzer._find_boundary_index_smart(x2, col_boundaries, tolerance)

        # 边界检查和修正
        start_row = max(0, min(start_row, rows - 1))
        start_col = max(0, min(start_col, cols - 1))
        end_row = max(start_row, min(end_row - 1, rows - 1))
        end_col = max(start_col, min(end_col - 1, cols - 1))

        # 计算跨度
        row_span = end_row - start_row + 1
        col_span = end_col - start_col + 1
        is_merged = row_span > 1 or col_span > 1

        # 填充矩阵
        for row in range(start_row, end_row + 1):
            for col in range(start_col, end_col + 1):
                if 0 <= row < rows and 0 <= col < cols:
                    matrix[row][col] = cell_id

        # 记录单元格位置信息
        cell_positions[cell_id] = {
            'row': start_row,
            'col': start_col,
            'row_span': row_span,
            'col_span': col_span,
            'is_merged': is_merged,
            'physical_bounds': (x1, y1, x2, y2),
            'cell_object': cell
        }

    return {
        'matrix': matrix,
        'cell_positions': cell_positions
    }
```

### 6. 结果应用到单元格对象

**文件**: `labelme/widgets/table_controller.py`
**方法**: `_apply_analysis_result()`

```python
def _apply_analysis_result(self, analysis_result: Dict):
    """将分析结果应用到单元格对象"""
    cell_positions = analysis_result['cell_positions']

    # 存储到撤销栈
    self.canvas.storeShapes()

    for cell_id, position_info in cell_positions.items():
        cell = position_info['cell_object']

        # 获取完整的逻辑位置信息
        start_row = position_info['row']
        start_col = position_info['col']
        row_span = position_info['row_span']
        col_span = position_info['col_span']

        # 计算end_row和end_col
        end_row = start_row + row_span - 1
        end_col = start_col + col_span - 1

        # 设置逻辑位置
        if hasattr(cell, 'set_logical_location'):
            cell.set_logical_location(
                start_row=start_row,
                end_row=end_row,
                start_col=start_col,
                end_col=end_col
            )

        # 更新单元格标签
        if hasattr(cell, 'label'):
            if row_span > 1 or col_span > 1:
                cell.label = f"merged_{start_row}_{start_col}({row_span}x{col_span})"
            else:
                cell.label = f"cell_{start_row}_{start_col}"

        # 设置合并标识
        if hasattr(cell, 'table_properties'):
            cell.table_properties['is_merged'] = row_span > 1 or col_span > 1
            cell.table_properties['row_span'] = row_span
            cell.table_properties['col_span'] = col_span

    # 更新显示
    self.canvas.update()

    # 刷新结构视图
    if hasattr(self, 'structure_widget'):
        self._refresh_structure_widget()
```

### 7. 结构视图更新

#### 7.1 刷新结构视图

```python
def _refresh_structure_widget(self):
    """刷新结构视图显示"""
    if not hasattr(self, 'structure_widget'):
        return

    try:
        # 动态构造结构视图需要的数据格式
        grid_data = self._build_grid_data_for_structure_view()
        if grid_data:
            self.structure_widget.update_structure_view(grid_data)
    except Exception as e:
        LOGGER.error(f"刷新结构视图错误: {e}")
```

#### 7.2 构建视图数据

```python
def _build_grid_data_for_structure_view(self):
    """构造结构视图需要的数据格式"""
    if not self.table_cells:
        return None

    # 分析所有单元格，计算表格尺寸
    max_row = 0
    max_col = 0
    cells_data = []

    for cell in self.table_cells:
        try:
            # 获取完整的逻辑位置
            lloc = cell.get_logical_location()
            if not lloc:
                continue

            # 更新最大行列数
            max_row = max(max_row, lloc.get('end_row', 0))
            max_col = max(max_col, lloc.get('end_col', 0))

            # 构建单元格数据
            cell_data = {
                'row': lloc.get('start_row', 0),
                'col': lloc.get('start_col', 0),
                'text': cell.get_cell_text() if hasattr(cell, 'get_cell_text') else '',
                'border': cell.get_border_style() if hasattr(cell, 'get_border_style') else {},
                'cell_id': cell.table_properties.get('cell_id', ''),
                # 包含完整的逻辑位置信息
                'logical_location': {
                    'start_row': lloc.get('start_row', 0),
                    'end_row': lloc.get('end_row', 0),
                    'start_col': lloc.get('start_col', 0),
                    'end_col': lloc.get('end_col', 0)
                },
                # 包含表头信息
                'table_properties': {
                    'header': cell.get_header() if hasattr(cell, 'get_header') else False,
                    'table_type': cell.get_table_type() if hasattr(cell, 'get_table_type') else 0,
                    'cell_id': cell.table_properties.get('cell_id', '')
                }
            }
            cells_data.append(cell_data)

        except Exception as e:
            continue

    if not cells_data:
        return None

    return {
        'rows': max_row + 1,
        'cols': max_col + 1,
        'cells': cells_data
    }
```

### 8. 容差控制系统

**文件**: `labelme/app_tableme.py`
**类**: `AlignmentToolWidget`

```python
def update_analysis_mode(self):
    """更新表格分析模式的容差设置"""
    try:
        current_tolerance = self.tolerance_slider.value()

        # 调用table_analyzer的全局函数
        from labelme.utils.table_analyzer import set_analysis_mode
        set_analysis_mode(smart=True, tolerance=float(current_tolerance), debug=True)

        # 在状态栏显示更新信息
        if hasattr(self.main_window, 'statusBar'):
            self.main_window.statusBar().showMessage(
                f"✅ 对齐容差已更新: {current_tolerance}px", 3000
            )

    except Exception as e:
        LOGGER.error(f"更新分析模式失败: {e}")
```

**全局配置函数**:

```python
def set_analysis_mode(smart: bool = True, tolerance: float = 5.0, debug: bool = True):
    """设置全局分析模式"""
    TableAnalyzer.enable_smart_analysis = smart
    TableAnalyzer.default_tolerance = tolerance
    TableAnalyzer.debug_output = debug

    mode_name = "智能分析" if smart else "精确分析"
    LOGGER.debug(f"[CONFIG] 分析模式设置: {mode_name}, 容差={tolerance}px")
```

## 分析模式对比

### 智能分析模式 vs 精确分析模式

| 特性 | 智能分析模式 | 精确分析模式 |
|------|-------------|-------------|
| **容差处理** | ✅ 支持容差修正 | ❌ 严格按坐标 |
| **边界检测** | ✅ 自动检测并修正 | ❌ 不处理边界问题 |
| **适用场景** | 手工标注，存在误差 | 程序生成，精确对齐 |
| **性能** | 稍慢（需要检测和修正） | 较快（直接处理） |
| **鲁棒性** | 高（容错能力强） | 低（对输入要求严格） |

### 边界问题示例

```text
原始边界（存在误差）:
行边界: [100, 150.2, 200.1, 250.3]
列边界: [50, 100.1, 149.9, 200.2]

智能分析修正后:
行边界: [100, 150, 200, 250]    # 容差内的边界被合并
列边界: [50, 100, 150, 200]     # 149.9和150.1被合并为150
```

## 调用时机

表格结构分析在以下情况下会被触发：

### 1. 手动触发

- 用户点击"分析表格结构"按钮
- 按下快捷键 `A`

### 2. 自动触发

- 单元格对齐操作后 (`align_top`, `align_bottom`, `align_left`, `align_right`)
- 单元格拆分操作后 (`_split_cells_by_row`, `_split_cells_by_column`)
- 快速生成表格后 (`quick_generate_table`)
- 应用表格对齐后 (`apply_table_alignment`)

### 3. 延迟触发

- 使用 `QtCore.QTimer.singleShot(100, self.analyze_table_structure)` 确保UI操作完成后再分析

## 性能优化

### 1. 缓存机制

- 分析结果缓存在 `TableController.current_table_grid`
- 避免重复分析相同的单元格集合

### 2. 增量更新

- 结构视图支持局部更新 (`update_cell_border_styles`)
- 避免全量重建视图

### 3. 异步处理

- 使用定时器延迟执行，避免阻塞UI线程
- 大量单元格时显示进度提示

## 错误处理

### 1. 输入验证

```python
if not cells or len(cells) < 1:
    raise ValueError("需要至少1个单元格")

if not all(isinstance(cell, TableCellShape) for cell in cells):
    raise TypeError("所有输入必须是TableCellShape对象")
```

### 2. 边界检查

```python
# 确保索引在有效范围内
start_row = max(0, min(start_row, rows - 1))
start_col = max(0, min(start_col, cols - 1))
end_row = max(start_row, min(end_row - 1, rows - 1))
end_col = max(start_col, min(end_col - 1, cols - 1))
```

### 3. 异常捕获

```python
try:
    result = TableAnalyzer.analyze_cells_to_grid(cells)
    # ... 处理结果
except Exception as e:
    QtWidgets.QMessageBox.critical(self, "分析失败", f"表格结构分析失败:\n{e}")
```

## 调试支持

### 1. 详细日志输出

```python
def print_grid_analysis(result: Dict) -> None:
    """打印表格分析结果，便于调试"""
    LOGGER.debug(f"表格大小: {result['grid']['rows']}行 x {result['grid']['cols']}列")
    LOGGER.debug(f"行边界: {result['boundaries']['rows']}")
    LOGGER.debug(f"列边界: {result['boundaries']['cols']}")

    # 打印逻辑矩阵
    matrix = result['grid']['matrix']
    for i, row in enumerate(matrix):
        row_str = " | ".join([f"{str(cell)[-4:]:>4}" if cell else "   ." for cell in row])
        LOGGER.debug(f"Row {i}: {row_str}")

    # 打印单元格信息
    for cell_id, pos in result['cell_positions'].items():
        merge_info = f" (合并 {pos['row_span']}x{pos['col_span']})" if pos['is_merged'] else ""
        LOGGER.debug(f"Cell {str(cell_id)[-4:]}: ({pos['row']}, {pos['col']}){merge_info}")
```

### 2. 智能分析元数据

```python
# 智能分析额外信息
smart_metadata = result.get('_smart_metadata')
if smart_metadata:
    LOGGER.debug(f"分析模式: {smart_metadata.get('analysis_mode', 'precise')}")
    LOGGER.debug(f"容差设置: {smart_metadata.get('tolerance_used', 0)} 像素")
    if smart_metadata.get('boundary_issues', {}).get('has_issues', False):
        LOGGER.debug(f"修正状态: 已自动修正边界问题")
```

## 扩展性设计

### 1. 插件化分析模式

- 通过 `TableAnalyzer.enable_smart_analysis` 控制分析模式
- 可以轻松添加新的分析算法

### 2. 可配置参数

- 容差值可通过UI滑块实时调整
- 调试输出可通过配置开关控制

### 3. 结果格式标准化

- 分析结果采用统一的字典格式
- 便于不同组件间的数据交换

## 完整调用链流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as app_tableme.py
    participant Controller as TableController
    participant Analyzer as TableAnalyzer
    participant Widget as StructureWidget

    User->>App: 点击"分析表格结构"按钮
    App->>Controller: get_table_cells()
    Controller-->>App: 返回单元格列表

    App->>Analyzer: analyze_cells_to_grid(cells)
    Analyzer->>Analyzer: _analyze_with_smart_mode()
    Analyzer->>Analyzer: _detect_boundary_issues()
    Analyzer->>Analyzer: _merge_close_boundaries()
    Analyzer->>Analyzer: _build_logical_grid_smart()
    Analyzer-->>App: 返回分析结果

    App->>Analyzer: print_grid_analysis(result)
    App->>Controller: _apply_analysis_result(result)
    Controller->>Controller: 设置单元格逻辑位置
    Controller->>Controller: _refresh_structure_widget()
    Controller->>Controller: _build_grid_data_for_structure_view()
    Controller->>Widget: update_structure_view(grid_data)

    Widget->>Widget: 更新表格视图
    Widget->>Widget: 应用合并单元格
    Widget-->>User: 显示逻辑结构
```

## 总结

表格结构分析系统是 TableLabelMe 的核心功能之一，它将用户标注的物理单元格转换为逻辑表格结构。系统采用智能分析模式，支持容差修正，能够有效处理手工标注中的误差问题。通过模块化设计和标准化接口，系统具有良好的可扩展性和维护性。

### 主要特点

- **智能容差修正**: 自动处理边界不对齐问题
- **合并单元格支持**: 正确识别和处理跨行跨列单元格
- **实时可视化**: 分析结果实时更新到结构视图
- **性能优化**: 缓存机制和增量更新
- **调试友好**: 详细的日志输出和错误处理

### 技术亮点

1. **双模式分析**: 智能模式和精确模式可切换
2. **容差算法**: 基于距离的边界合并算法
3. **增量更新**: 避免全量重建，提升性能
4. **异常处理**: 完善的错误处理和边界检查
5. **可视化支持**: 实时更新逻辑结构视图

### 应用场景

- **手工标注**: 处理用户手工绘制的不精确单元格
- **API导入**: 处理第三方API识别结果的微小误差
- **批量处理**: 支持大量单元格的高效分析
- **实时编辑**: 支持编辑过程中的动态分析更新

import numpy as np


def calculate_iou(boxA, boxB):
    """计算两个边界框的IoU"""
    xA = max(boxA[0], boxB[0])
    yA = max(boxA[1], boxB[1])
    xB = min(boxA[2], boxB[2])
    yB = min(boxA[3], boxB[3])
    inter_area = max(0, xB - xA) * max(0, yB - yA)
    boxA_area = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
    boxB_area = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])
    union_area = float(boxA_area + boxB_area - inter_area)
    return inter_area / union_area if union_area > 0 else 0


def calculate_detection_metrics(gt_boxes, pred_boxes, iou_thresholds):
    """
    在一系列IoU阈值下计算单元格检测的Precision, Recall, F1-score。

    Args:
        gt_boxes (list): Ground-truth 边界框列表。 e.g., [[x1,y1,x2,y2], ...]
        pred_boxes (list): Prediction 边界框列表。 e.g., [[x1,y1,x2,y2], ...]
        iou_thresholds (list): 要评估的IoU阈值列表。 e.g., [0.6, 0.7, 0.8, 0.9]

    Returns:
        dict: 一个字典，键是IoU阈值，值是包含p/r/f1的字典。
    """
    results = {}

    if not gt_boxes or not pred_boxes:
        # 如果任一列表为空，则无法进行匹配
        for threshold in iou_thresholds:
            tp, fp, fn = 0, len(pred_boxes), len(gt_boxes)
            precision = 0.0
            recall = 0.0 if fn == 0 else 0.0  # 保持一致性
            f1_score = 0.0
            results[f"iou_@{threshold}"] = {
                'precision': precision, 'recall': recall, 'f1_score': f1_score,
                'tp': tp, 'fp': fp, 'fn': fn
            }
        return results

    # 1. 构建IoU矩阵
    num_gt = len(gt_boxes)
    num_pred = len(pred_boxes)
    iou_matrix = np.zeros((num_gt, num_pred))
    for i in range(num_gt):
        for j in range(num_pred):
            iou_matrix[i, j] = calculate_iou(gt_boxes[i], pred_boxes[j])

    # 2. 对每个阈值进行评估
    for threshold in iou_thresholds:
        # 3. 执行贪心匹配
        # gt_matched_pred_idx[i] = j 表示 gt_box[i] 与 pred_box[j] 匹配
        # 如果没有匹配，则为 -1
        gt_matched_pred_idx = np.full(num_gt, -1, dtype=int)
        pred_matched_gt_idx = np.full(num_pred, -1, dtype=int)

        # 优先匹配IoU值最高的对
        sorted_iou_indices = np.dstack(np.unravel_index(np.argsort(-iou_matrix.ravel()), (num_gt, num_pred)))[0]

        for gt_idx, pred_idx in sorted_iou_indices:
            if iou_matrix[gt_idx, pred_idx] < threshold:
                break  # 因为已经排序，后续的IoU值只会更小

            # 如果gt或pred已经被匹配，则跳过
            if gt_matched_pred_idx[gt_idx] != -1 or pred_matched_gt_idx[pred_idx] != -1:
                continue

            # 记录匹配
            gt_matched_pred_idx[gt_idx] = pred_idx
            pred_matched_gt_idx[pred_idx] = gt_idx

        # 4. 计算 TP, FP, FN
        tp = int(np.sum(gt_matched_pred_idx != -1))
        fp = int(num_pred - tp)
        fn = int(num_gt - tp)

        # 5. 计算指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

        results[f"iou_@{threshold}"] = {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'tp': tp,
            'fp': fp,
            'fn': fn
        }

    return results


if __name__ == '__main__':
    # --- 示例 ---
    # Ground-Truth 框
    gt_boxes = [
        [50, 50, 150, 100],  # GT 1: Good match target
        [200, 200, 300, 250],  # GT 2: Poor match target
        [50, 200, 150, 250]  # GT 3: Missed by prediction (FN)
    ]
    # Prediction 框
    pred_boxes = [
        [51, 52, 149, 101],  # Pred 1: Good match (IoU > 0.9)
        [210, 210, 310, 260],  # Pred 2: Poor match (IoU ≈ 0.6-0.7)
        [400, 400, 500, 450]  # Pred 3: False positive (FP)
    ]
    # 要评估的IoU阈值
    iou_thresholds_to_test = [0.6, 0.7, 0.8, 0.9]

    # 进行评测
    metrics = calculate_detection_metrics(gt_boxes, pred_boxes, iou_thresholds_to_test)

    # 打印结果
    import json

    print(json.dumps(metrics, indent=4))
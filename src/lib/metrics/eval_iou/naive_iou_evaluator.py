"""
IoU评估工具

本模块用于评估表格结构识别中的单元格检测准确性。主要功能包括：

1. 解析Ground Truth JSON文件，提取单元格边界框坐标
2. 解析预测结果TXT文件，提取单元格边界框坐标
3. 计算不同IoU阈值下的precision、recall、f1-score
4. 批量处理目录中的所有匹配文件对
5. 汇总统计所有指标的均值

使用示例:
    from naive_iou_evaluator import IoUEvaluator, evaluate_iou_metrics

    # 类接口使用
    evaluator = IoUEvaluator(iou_thresholds=[0.6, 0.7, 0.8, 0.9])
    result = evaluator.evaluate_batch(
        gt_dir="path/to/ground_truth_annos",
        pred_dir="path/to/preds_logis",
        verbose=True
    )

    # 便利函数接口
    result = evaluate_iou_metrics(
        gt_dir="path/to/ground_truth_annos",
        pred_dir="path/to/preds_logis",
        iou_thresholds=[0.6, 0.7, 0.8, 0.9]
    )

    print(f"平均F1分数@0.8: {result['iou_@0.8']['f1_score']:.4f}")

文件格式要求:
- GT文件: *_table_annotation.json，包含cells数组和bbox字段
- 预测文件: *.jpg.txt或*.png.txt，每行4个点坐标，由";"分隔

作者: LORE-TSR项目组
日期: 2025-01-31
"""

import numpy as np
import json
import os
import glob
from typing import List, Dict, Tuple, Optional


class IoUEvaluator:
    """
    IoU评估器，用于批量计算单元格检测的IoU指标

    该类提供了完整的IoU评估功能，包括文件解析、指标计算和批量处理。
    支持多个IoU阈值同时评估，并提供详细的统计信息。
    """

    def __init__(self, iou_thresholds: Optional[List[float]] = None):
        """
        初始化IoU评估器

        Args:
            iou_thresholds: IoU阈值列表，默认为[0.6, 0.7, 0.8, 0.9]
        """
        self.iou_thresholds = iou_thresholds or [0.6, 0.7, 0.8, 0.9]

    def find_matching_files(self, gt_dir: str, pred_dir: str) -> List[Tuple[str, str]]:
        """
        在两个目录中找到匹配的文件对

        Args:
            gt_dir: Ground Truth目录路径
            pred_dir: 预测结果目录路径

        Returns:
            匹配的文件对列表 [(gt_file, pred_file), ...]

        Raises:
            FileNotFoundError: 目录不存在时抛出异常
        """
        if not os.path.exists(gt_dir):
            raise FileNotFoundError(f"Ground Truth目录不存在: {gt_dir}")
        if not os.path.exists(pred_dir):
            raise FileNotFoundError(f"预测结果目录不存在: {pred_dir}")

        matching_pairs = []

        # 获取所有GT JSON文件
        gt_pattern = os.path.join(gt_dir, "*_table_annotation.json")
        gt_files = glob.glob(gt_pattern)

        for gt_file in gt_files:
            # 从GT文件名提取前缀
            gt_basename = os.path.basename(gt_file)
            # 移除 "_table_annotation.json" 后缀
            if gt_basename.endswith("_table_annotation.json"):
                prefix = gt_basename[:-len("_table_annotation.json")]
            else:
                continue  # 跳过不符合命名规范的文件

            # 查找对应的预测文件
            pred_candidates = [
                os.path.join(pred_dir, f"{prefix}.jpg.txt"),
                os.path.join(pred_dir, f"{prefix}.png.txt")
            ]

            pred_file = None
            for candidate in pred_candidates:
                if os.path.exists(candidate):
                    pred_file = candidate
                    break

            if pred_file:
                matching_pairs.append((gt_file, pred_file))
            else:
                print(f"警告: 未找到与 {gt_basename} 匹配的预测文件")

        return matching_pairs


    def parse_prediction_file(self, pred_file_path: str) -> List[List[float]]:
        """
        解析预测文件，提取单元格坐标

        Args:
            pred_file_path: 预测文件路径

        Returns:
            边界框列表，格式为 [[x1, y1, x2, y2], ...]

        Note:
            预测文件格式：每行包含四个点的坐标 x1,y1;x2,y2;x3,y3;x4,y4
        """
        pred_boxes = []

        try:
            with open(pred_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        # 解析四个点的坐标
                        points = line.split(';')
                        if len(points) != 4:
                            print(f"警告: {pred_file_path} 第{line_num}行格式错误，跳过")
                            continue

                        coords = []
                        for point in points:
                            x, y = point.split(',')
                            coords.extend([float(x), float(y)])

                        # 转换为边界框格式 [x1, y1, x2, y2]
                        x_coords = [coords[0], coords[2], coords[4], coords[6]]
                        y_coords = [coords[1], coords[3], coords[5], coords[7]]

                        x_min, x_max = min(x_coords), max(x_coords)
                        y_min, y_max = min(y_coords), max(y_coords)

                        pred_boxes.append([x_min, y_min, x_max, y_max])

                    except (ValueError, IndexError) as e:
                        print(f"警告: {pred_file_path} 第{line_num}行解析错误: {e}")
                        continue

        except FileNotFoundError:
            raise FileNotFoundError(f"预测文件不存在: {pred_file_path}")
        except Exception as e:
            raise RuntimeError(f"解析预测文件时发生错误: {e}")

        return pred_boxes

    def parse_ground_truth_file(self, gt_file_path: str) -> List[List[float]]:
        """
        解析真实标注文件，提取单元格坐标

        Args:
            gt_file_path: GT文件路径

        Returns:
            边界框列表，格式为 [[x1, y1, x2, y2], ...]

        Note:
            GT文件格式：JSON文件，每个cell包含bbox信息，有p1,p2,p3,p4四个点
        """
        gt_boxes = []

        try:
            with open(gt_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            for cell_idx, cell in enumerate(data['cells']):
                bbox = cell.get('bbox')
                if bbox is None:
                    continue

                try:
                    # 提取四个点的坐标
                    p1, p2, p3, p4 = bbox['p1'], bbox['p2'], bbox['p3'], bbox['p4']

                    # 计算边界框
                    x_coords = [p1[0], p2[0], p3[0], p4[0]]
                    y_coords = [p1[1], p2[1], p3[1], p4[1]]

                    x_min, x_max = min(x_coords), max(x_coords)
                    y_min, y_max = min(y_coords), max(y_coords)

                    gt_boxes.append([x_min, y_min, x_max, y_max])

                except (KeyError, TypeError, IndexError) as e:
                    print(f"警告: {gt_file_path} 第{cell_idx}个单元格bbox解析错误: {e}")
                    continue

        except FileNotFoundError:
            raise FileNotFoundError(f"GT文件不存在: {gt_file_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"GT文件JSON格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"解析GT文件时发生错误: {e}")

        return gt_boxes

    def calculate_iou(self, boxA: List[float], boxB: List[float]) -> float:
        """
        计算两个边界框的IoU

        Args:
            boxA: 边界框A，格式为 [x1, y1, x2, y2]
            boxB: 边界框B，格式为 [x1, y1, x2, y2]

        Returns:
            IoU值，范围为 [0, 1]
        """
        xA = max(boxA[0], boxB[0])
        yA = max(boxA[1], boxB[1])
        xB = min(boxA[2], boxB[2])
        yB = min(boxA[3], boxB[3])
        inter_area = max(0, xB - xA) * max(0, yB - yA)
        boxA_area = (boxA[2] - boxA[0]) * (boxA[3] - boxA[1])
        boxB_area = (boxB[2] - boxB[0]) * (boxB[3] - boxB[1])
        union_area = float(boxA_area + boxB_area - inter_area)
        return inter_area / union_area if union_area > 0 else 0


    def calculate_detection_metrics(self, gt_boxes: List[List[float]], pred_boxes: List[List[float]],
                                  iou_thresholds: Optional[List[float]] = None) -> Dict[str, Dict[str, float]]:
        """
        在一系列IoU阈值下计算单元格检测的Precision, Recall, F1-score

        Args:
            gt_boxes: Ground-truth 边界框列表，格式为 [[x1,y1,x2,y2], ...]
            pred_boxes: Prediction 边界框列表，格式为 [[x1,y1,x2,y2], ...]
            iou_thresholds: 要评估的IoU阈值列表，默认使用实例的阈值

        Returns:
            字典，键是IoU阈值，值是包含precision/recall/f1_score的字典
        """
        if iou_thresholds is None:
            iou_thresholds = self.iou_thresholds

        results = {}

        if not gt_boxes or not pred_boxes:
            # 如果任一列表为空，则无法进行匹配
            for threshold in iou_thresholds:
                tp, fp, fn = 0, len(pred_boxes), len(gt_boxes)
                precision = 0.0
                recall = 0.0 if fn == 0 else 0.0  # 保持一致性
                f1_score = 0.0
                results[f"iou_@{threshold}"] = {
                    'precision': precision, 'recall': recall, 'f1_score': f1_score,
                    'tp': tp, 'fp': fp, 'fn': fn
                }
            return results

        # 1. 构建IoU矩阵
        num_gt = len(gt_boxes)
        num_pred = len(pred_boxes)
        iou_matrix = np.zeros((num_gt, num_pred))
        for i in range(num_gt):
            for j in range(num_pred):
                iou_matrix[i, j] = self.calculate_iou(gt_boxes[i], pred_boxes[j])

        # 2. 对每个阈值进行评估
        for threshold in iou_thresholds:
            # 3. 执行贪心匹配
            # gt_matched_pred_idx[i] = j 表示 gt_box[i] 与 pred_box[j] 匹配
            # 如果没有匹配，则为 -1
            gt_matched_pred_idx = np.full(num_gt, -1, dtype=int)
            pred_matched_gt_idx = np.full(num_pred, -1, dtype=int)

            # 优先匹配IoU值最高的对
            sorted_iou_indices = np.dstack(np.unravel_index(np.argsort(-iou_matrix.ravel()), (num_gt, num_pred)))[0]

            for gt_idx, pred_idx in sorted_iou_indices:
                if iou_matrix[gt_idx, pred_idx] < threshold:
                    break  # 因为已经排序，后续的IoU值只会更小

                # 如果gt或pred已经被匹配，则跳过
                if gt_matched_pred_idx[gt_idx] != -1 or pred_matched_gt_idx[pred_idx] != -1:
                    continue

                # 记录匹配
                gt_matched_pred_idx[gt_idx] = pred_idx
                pred_matched_gt_idx[pred_idx] = gt_idx

            # 4. 计算 TP, FP, FN
            tp = int(np.sum(gt_matched_pred_idx != -1))
            fp = int(num_pred - tp)
            fn = int(num_gt - tp)

            # 5. 计算指标
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

            results[f"iou_@{threshold}"] = {
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'tp': tp,
                'fp': fp,
                'fn': fn
            }

        return results

    def evaluate_single_pair(self, gt_file: str, pred_file: str, verbose: bool = False) -> Dict[str, Dict[str, float]]:
        """
        评估单个文件对的IoU指标

        Args:
            gt_file: GT文件路径
            pred_file: 预测文件路径
            verbose: 是否打印详细信息

        Returns:
            包含各IoU阈值下指标的字典

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        if verbose:
            print(f"评估文件对:")
            print(f"  GT文件: {os.path.basename(gt_file)}")
            print(f"  预测文件: {os.path.basename(pred_file)}")

        try:
            # 解析文件
            gt_boxes = self.parse_ground_truth_file(gt_file)
            pred_boxes = self.parse_prediction_file(pred_file)

            if verbose:
                print(f"  GT单元格数量: {len(gt_boxes)}")
                print(f"  预测单元格数量: {len(pred_boxes)}")

            # 计算指标
            metrics = self.calculate_detection_metrics(gt_boxes, pred_boxes)

            if verbose:
                for threshold_key, threshold_metrics in metrics.items():
                    print(f"  {threshold_key}: P={threshold_metrics['precision']:.4f}, "
                          f"R={threshold_metrics['recall']:.4f}, F1={threshold_metrics['f1_score']:.4f}")

            return metrics

        except Exception as e:
            print(f"错误: 处理文件对时发生异常: {e}")
            # 返回空指标
            empty_metrics = {}
            for threshold in self.iou_thresholds:
                empty_metrics[f"iou_@{threshold}"] = {
                    'precision': 0.0, 'recall': 0.0, 'f1_score': 0.0,
                    'tp': 0, 'fp': 0, 'fn': 0
                }
            return empty_metrics

    def evaluate_batch(self, gt_dir: str, pred_dir: str, verbose: bool = True) -> Dict[str, float]:
        """
        批量评估目录中的所有文件对

        Args:
            gt_dir: Ground Truth目录路径
            pred_dir: 预测结果目录路径
            verbose: 是否打印详细信息

        Returns:
            包含平均指标和统计信息的字典

        Raises:
            FileNotFoundError: 目录不存在
            ValueError: 没有找到匹配的文件对
        """
        if verbose:
            print(f"开始批量评估IoU指标...")
            print(f"Ground Truth目录: {gt_dir}")
            print(f"预测结果目录: {pred_dir}")
            print(f"IoU阈值: {self.iou_thresholds}")

        # 找到匹配的文件对
        matching_pairs = self.find_matching_files(gt_dir, pred_dir)

        if not matching_pairs:
            raise ValueError(f"未找到匹配的文件对，请检查目录路径和文件命名")

        if verbose:
            print(f"找到 {len(matching_pairs)} 对匹配文件")

        # 收集所有指标
        all_metrics = []
        successful_pairs = 0
        failed_pairs = 0

        for i, (gt_file, pred_file) in enumerate(matching_pairs, 1):
            try:
                if verbose:
                    print(f"\n--- 处理第 {i}/{len(matching_pairs)} 对文件 ---")

                metrics = self.evaluate_single_pair(gt_file, pred_file, verbose=verbose)

                # 检查是否有有效指标
                has_valid_metrics = any(
                    m['precision'] > 0 or m['recall'] > 0 or m['f1_score'] > 0
                    for m in metrics.values()
                )

                if has_valid_metrics:
                    all_metrics.append(metrics)
                    successful_pairs += 1
                else:
                    failed_pairs += 1
                    if verbose:
                        print(f"  警告: 该文件对未产生有效指标")

            except Exception as e:
                failed_pairs += 1
                if verbose:
                    print(f"  错误: 处理文件对失败: {e}")
                continue

        if not all_metrics:
            raise ValueError("没有成功处理的文件对，无法计算平均指标")

        # 计算平均指标
        result = self._calculate_average_metrics(all_metrics)
        result.update({
            'total_pairs': len(matching_pairs),
            'successful_pairs': successful_pairs,
            'failed_pairs': failed_pairs
        })

        if verbose:
            print(f"\n{'=' * 50}")
            print(f"IoU评估完成!")
            print(f"{'=' * 50}")
            print(f"处理文件对: {successful_pairs}/{len(matching_pairs)}")
            for threshold in self.iou_thresholds:
                threshold_key = f"iou_@{threshold}"
                print(f"{threshold_key}: P={result[threshold_key]['precision']:.4f}, "
                      f"R={result[threshold_key]['recall']:.4f}, F1={result[threshold_key]['f1_score']:.4f}")

            if failed_pairs > 0:
                print(f"处理失败: {failed_pairs} 个文件对")

        return result

    def _calculate_average_metrics(self, all_metrics: List[Dict[str, Dict[str, float]]]) -> Dict[str, Dict[str, float]]:
        """
        计算所有指标的平均值

        Args:
            all_metrics: 所有文件对的指标列表

        Returns:
            平均指标字典
        """
        if not all_metrics:
            return {}

        # 初始化累加器
        avg_metrics = {}
        for threshold in self.iou_thresholds:
            threshold_key = f"iou_@{threshold}"
            avg_metrics[threshold_key] = {
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'tp': 0,
                'fp': 0,
                'fn': 0
            }

        # 累加所有指标
        for metrics in all_metrics:
            for threshold_key, threshold_metrics in metrics.items():
                if threshold_key in avg_metrics:
                    for metric_name, metric_value in threshold_metrics.items():
                        avg_metrics[threshold_key][metric_name] += metric_value

        # 计算平均值
        num_metrics = len(all_metrics)
        for threshold_key in avg_metrics:
            for metric_name in ['precision', 'recall', 'f1_score']:
                avg_metrics[threshold_key][metric_name] /= num_metrics
            # tp, fp, fn 保持累加值

        return avg_metrics


# 便利函数，保持向后兼容性
def evaluate_iou_metrics(gt_dir: str, pred_dir: str,
                        iou_thresholds: Optional[List[float]] = None,
                        verbose: bool = True) -> Dict[str, float]:
    """
    评估两个目录中所有匹配文件对的IoU指标（便利函数）

    Args:
        gt_dir: Ground Truth目录路径
        pred_dir: 预测结果目录路径
        iou_thresholds: IoU阈值列表，默认为[0.6, 0.7, 0.8, 0.9]
        verbose: 是否打印详细信息

    Returns:
        包含平均指标和统计信息的字典

    Raises:
        FileNotFoundError: 目录不存在
        ValueError: 没有找到匹配的文件对
    """
    evaluator = IoUEvaluator(iou_thresholds=iou_thresholds)
    return evaluator.evaluate_batch(gt_dir, pred_dir, verbose=verbose)


# 保持向后兼容的独立函数
def parse_prediction_file(pred_file_path: str) -> List[List[float]]:
    """
    解析预测文件，提取单元格坐标（向后兼容函数）

    Args:
        pred_file_path: 预测文件路径

    Returns:
        边界框列表，格式为 [[x1, y1, x2, y2], ...]
    """
    evaluator = IoUEvaluator()
    return evaluator.parse_prediction_file(pred_file_path)


def parse_ground_truth_file(gt_file_path: str) -> List[List[float]]:
    """
    解析真实标注文件，提取单元格坐标（向后兼容函数）

    Args:
        gt_file_path: GT文件路径

    Returns:
        边界框列表，格式为 [[x1, y1, x2, y2], ...]
    """
    evaluator = IoUEvaluator()
    return evaluator.parse_ground_truth_file(gt_file_path)


def calculate_iou(boxA: List[float], boxB: List[float]) -> float:
    """
    计算两个边界框的IoU（向后兼容函数）

    Args:
        boxA: 边界框A，格式为 [x1, y1, x2, y2]
        boxB: 边界框B，格式为 [x1, y1, x2, y2]

    Returns:
        IoU值，范围为 [0, 1]
    """
    evaluator = IoUEvaluator()
    return evaluator.calculate_iou(boxA, boxB)


def calculate_detection_metrics(gt_boxes: List[List[float]], pred_boxes: List[List[float]],
                              iou_thresholds: List[float]) -> Dict[str, Dict[str, float]]:
    """
    在一系列IoU阈值下计算单元格检测的Precision, Recall, F1-score（向后兼容函数）

    Args:
        gt_boxes: Ground-truth 边界框列表，格式为 [[x1,y1,x2,y2], ...]
        pred_boxes: Prediction 边界框列表，格式为 [[x1,y1,x2,y2], ...]
        iou_thresholds: 要评估的IoU阈值列表

    Returns:
        字典，键是IoU阈值，值是包含precision/recall/f1_score的字典
    """
    evaluator = IoUEvaluator(iou_thresholds=iou_thresholds)
    return evaluator.calculate_detection_metrics(gt_boxes, pred_boxes)


if __name__ == '__main__':
    # # --- 使用示例 ---
    #
    # # 示例1: 使用类接口进行单个文件对评估
    # print("=== 示例1: 单个文件对评估 ===")
    # evaluator = IoUEvaluator(iou_thresholds=[0.6, 0.7, 0.8, 0.9])
    #
    # # 模拟数据
    # gt_boxes = [
    #     [50, 50, 150, 100],  # GT 1: Good match target
    #     [200, 200, 300, 250],  # GT 2: Poor match target
    #     [50, 200, 150, 250]  # GT 3: Missed by prediction (FN)
    # ]
    # pred_boxes = [
    #     [51, 52, 149, 101],  # Pred 1: Good match (IoU > 0.9)
    #     [210, 210, 310, 260],  # Pred 2: Poor match (IoU ≈ 0.6-0.7)
    #     [400, 400, 500, 450]  # Pred 3: False positive (FP)
    # ]
    #
    # # 计算指标
    # metrics = evaluator.calculate_detection_metrics(gt_boxes, pred_boxes)
    #
    # # 打印结果
    # import json
    # print("单个文件对评估结果:")
    # print(json.dumps(metrics, indent=2))

    print("\n=== 示例2: 批量目录评估 ===")
    print("使用方法:")
    print("evaluator = IoUEvaluator()")
    print("result = evaluator.evaluate_batch('gt_dir', 'pred_dir')")
    print("或者使用便利函数:")
    print("result = evaluate_iou_metrics('gt_dir', 'pred_dir')")

    evaluator = IoUEvaluator(iou_thresholds=[0.6, 0.7, 0.8, 0.9])
    gt_dir = '/aipdf-mlp/lanx/workspace/datasets/wired_optimizer/annos'
    # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark_official/center'
    # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark/center'
    pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark_epoch20/center'


    # gt_dir = '/aipdf-mlp/lanx/workspace/datasets/wired_optimizer/annos'
    # # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark_official/center'
    # pred_dir = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark/center'


    result = evaluator.evaluate_batch(gt_dir, pred_dir)
    print(json.dumps(result, indent=2))



    # print("\n=== 示例3: 向后兼容函数 ===")
    # # 使用向后兼容的独立函数
    # metrics_compat = calculate_detection_metrics(gt_boxes, pred_boxes, [0.6, 0.7, 0.8, 0.9])
    # print("向后兼容函数结果:")
    # print(json.dumps(metrics_compat, indent=2))
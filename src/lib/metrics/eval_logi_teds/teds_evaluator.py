import os
import glob
from typing import List, Tuple

# 导入真实的TEDS库
from table_recognition_metric import TEDS

try:
    from .table_converter import logi_json_to_html, logi_txt_to_html, logi_my_txt_to_html
except ImportError:
    from table_converter import logi_json_to_html, logi_txt_to_html, logi_my_txt_to_html


class TEDSEvaluator:
    """
    TEDS评估器，用于批量计算原始标注和预测标注之间的TEDS分数
    """
    
    def __init__(self):
        self.teds = TEDS()
    
    def find_matching_files(self, gt_dir: str, pred_dir: str) -> List[Tuple[str, str]]:
        """
        在两个目录中找到匹配的文件对
        
        Args:
            gt_dir: 原始标注目录（JSON文件）
            pred_dir: 预测标注目录（TXT文件）
            
        Returns:
            匹配的文件对列表 [(gt_file, pred_file), ...]
        """
        matching_pairs = []
        
        # 获取所有JSON文件
        json_files = glob.glob(os.path.join(gt_dir, "*.json"))
        
        for json_file in json_files:
            # 从JSON文件名提取基础名称
            base_name = os.path.basename(json_file)
            
            # 尝试不同的匹配模式
            possible_pred_names = []
            
            # 模式1: 直接替换扩展名
            possible_pred_names.append(base_name.replace('.json', '.txt'))
            
            # 模式2: 移除_table_annotation.json后缀，添加.txt
            if base_name.endswith('_table_annotation.json'):
                image_name = base_name.replace('_table_annotation.json', '')
                possible_pred_names.extend([
                    f"{image_name}.txt",
                    f"{image_name}.jpg.txt",
                    f"{image_name}.png.txt"
                ])
            
            # 查找匹配的预测文件
            for pred_name in possible_pred_names:
                pred_file = os.path.join(pred_dir, pred_name)
                if os.path.exists(pred_file):
                    matching_pairs.append((json_file, pred_file))
                    break
        
        return matching_pairs
    
    def evaluate_single_pair(self, gt_file: str, pred_file: str, debug: bool = False) -> float:
        """
        评估单个文件对的TEDS分数

        Args:
            gt_file: 原始标注文件路径
            pred_file: 预测标注文件路径
            debug: 是否打印调试信息

        Returns:
            TEDS分数
        """
        try:
            # 转换为HTML格式
            gt_html = logi_json_to_html(gt_file)
            # pred_html = logi_txt_to_html(pred_file)
            pred_html = logi_my_txt_to_html(pred_file)
            print(gt_html)
            print(pred_html)

            # 验证HTML是否有效
            if not gt_html or gt_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid ground truth HTML for {gt_file}")
                return 0.0

            if not pred_html or pred_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid prediction HTML for {pred_file}")
                return 0.0

            if debug:
                print(f"GT HTML length: {len(gt_html)}")
                print(f"Pred HTML length: {len(pred_html)}")
                print(f"GT HTML preview: {gt_html[:200]}...")
                print(f"Pred HTML preview: {pred_html[:200]}...")

            # 计算TEDS分数
            score = self.teds(gt_html, pred_html)

            # 验证分数的有效性
            if score < 0:
                print(f"Warning: Negative TEDS score {score} for {os.path.basename(gt_file)}")
                print(f"  GT file: {gt_file}")
                print(f"  Pred file: {pred_file}")
                if debug:
                    print(f"  GT HTML: {gt_html}")
                    print(f"  Pred HTML: {pred_html}")
                # 将负数分数设为0，或者保留原值用于调试
                return max(0.0, score)  # 可以改为 return score 来保留负数用于调试

            return score

        except Exception as e:
            print(f"Error processing {gt_file} and {pred_file}: {e}")
            import traceback
            traceback.print_exc()
            return 0.0
    
    def evaluate_directories(self, gt_dir: str, pred_dir: str, verbose: bool = True) -> dict:
        """
        评估两个目录中所有匹配文件的TEDS分数
        
        Args:
            gt_dir: 原始标注目录
            pred_dir: 预测标注目录
            verbose: 是否打印详细信息
            
        Returns:
            评估结果字典，包含平均分数、所有分数等信息
        """
        # 找到匹配的文件对
        matching_pairs = self.find_matching_files(gt_dir, pred_dir)
        
        if not matching_pairs:
            print(f"No matching files found between {gt_dir} and {pred_dir}")
            return {
                'average_score': 0.0,
                'total_pairs': 0,
                'scores': [],
                'file_pairs': []
            }
        
        if verbose:
            print(f"Found {len(matching_pairs)} matching file pairs")
        
        scores = []
        processed_pairs = []
        
        for i, (gt_file, pred_file) in enumerate(matching_pairs):
            if verbose:
                print(f"Processing {i+1}/{len(matching_pairs)}: {os.path.basename(gt_file)}")
            
            score = self.evaluate_single_pair(gt_file, pred_file)
            scores.append(score)
            processed_pairs.append((gt_file, pred_file))
            
            if verbose:
                print(f"  TEDS Score: {score:.4f}")
        
        # 计算平均分数
        average_score = sum(scores) / len(scores) if scores else 0.0
        
        result = {
            'average_score': average_score,
            'total_pairs': len(scores),
            'scores': scores,
            'file_pairs': processed_pairs
        }
        
        if verbose:
            print(f"\nEvaluation Results:")
            print(f"Total file pairs: {result['total_pairs']}")
            print(f"Average TEDS Score: {result['average_score']:.4f}")
            print(f"Min Score: {min(scores):.4f}" if scores else "N/A")
            print(f"Max Score: {max(scores):.4f}" if scores else "N/A")
        
        return result


def main():
    """
    主函数，可以通过命令行参数或直接修改路径来使用
    """
    import sys
    
    # 默认路径，可以根据需要修改
    # default_gt_dir = "/aipdf-mlp/lanx/workspace/datasets/wired_optimizer/annos"
    # default_pred_dir = "/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark_official/logi"

    default_gt_dir = "/aipdf-mlp/lanx/workspace/datasets/wired_optimizer/annos"
    # default_pred_dir = "/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark/logi"
    default_pred_dir = "/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wired_benchmark_epoch20/logi"

    # default_gt_dir = "/aipdf-mlp/lanx/workspace/datasets/wireless_optimizer/annos"
    # default_pred_dir = "/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wireless_benchmark_official/logi"

    # default_gt_dir = "/aipdf-mlp/lanx/workspace/datasets/wireless_optimizer/annos"
    # default_pred_dir = "/aipdf-mlp/lanx/workspace/projects/LORE-adapt/data/table/demo_wireless_benchmark/logi"


    # 如果提供了命令行参数，使用命令行参数
    if len(sys.argv) >= 3:
        gt_dir = sys.argv[1]
        pred_dir = sys.argv[2]
    else:
        gt_dir = default_gt_dir
        pred_dir = default_pred_dir
        print(f"Using default directories:")
        print(f"  Ground Truth: {gt_dir}")
        print(f"  Predictions: {pred_dir}")
        print(f"To use custom directories, run: python teds_evaluator.py <gt_dir> <pred_dir>")
        print()
    
    # 检查目录是否存在
    if not os.path.exists(gt_dir):
        print(f"Error: Ground truth directory does not exist: {gt_dir}")
        return
    
    if not os.path.exists(pred_dir):
        print(f"Error: Prediction directory does not exist: {pred_dir}")
        return
    
    # 创建评估器并运行评估
    evaluator = TEDSEvaluator()
    result = evaluator.evaluate_directories(gt_dir, pred_dir, verbose=True)
    
    # 保存结果到文件（可选）
    # import json
    # with open('teds_results.json', 'w') as f:
    #     json.dump(result, f, indent=2)


if __name__ == "__main__":
    main()

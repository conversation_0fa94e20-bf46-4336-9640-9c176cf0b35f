"""
解析器包初始化文件，提供统一的解析器接口导入。

该模块为LORE-TSR项目提供多种数据格式的解析器支持，
当前支持TableLabelMe格式和文件扫描功能，未来可扩展支持其他格式。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

from .base_parser import BaseParser
from .tablelabelme_parser import TableLabelMeParser
from .file_scanner import FileScanner
from .quality_filter import QualityFilter

__all__ = ['BaseParser', 'TableLabelMeParser', 'FileScanner', 'QualityFilter']

# src/lib/configs/config_examples.py
"""
配置示例文件 - TableLabelMe数据格式支持项目
提供详细的配置使用示例和说明，帮助用户理解如何创建和使用自定义配置文件

该模块包含完整的使用示例、命令行参数示例和自定义配置模板，
为用户提供便捷的配置管理指导。
"""

from typing import Dict, Any

# 配置文件使用示例
EXAMPLE_USAGE: str = """
# TableLabelMe数据格式配置使用示例

## 1. 使用预定义配置进行训练
python main.py ctdet_mid \\
    --dataset table \\
    --dataset_name TableLabelMe \\
    --data_config D:/workspace/projects/LORE-TSR-adapt/src/lib/configs/dataset_configs.py \\
    --exp_id train_tableme_chinese \\
    --wiz_2dpe \\
    --wiz_stacking \\
    --tsfm_layers 4 \\
    --stacking_layers 4 \\
    --batch_size 6 \\
    --master_batch 6 \\
    --arch resfpnhalf_18 \\
    --lr 1e-4 \\
    --K 500 \\
    --MK 1000 \\
    --num_epochs 200 \\
    --lr_step '100, 160' \\
    --gpus 0 \\
    --num_workers 16 \\
    --val_intervals 5

## 2. 使用真实数据进行测试训练
python main.py ctdet_mid \\
    --dataset table \\
    --dataset_name TableLabelMe \\
    --data_config D:/workspace/projects/LORE-TSR-adapt/src/lib/configs/dataset_configs.py \\
    --config_name tableme_chinese_test \\
    --exp_id test_tableme_real_data \\
    --batch_size 2 \\
    --num_epochs 5 \\
    --gpus 0

## 3. 使用完整数据集进行生产训练
python main.py ctdet_mid \\
    --dataset table \\
    --dataset_name TableLabelMe \\
    --data_config D:/workspace/projects/LORE-TSR-adapt/src/lib/configs/dataset_configs.py \\
    --config_name tableme_full \\
    --exp_id production_training \\
    --batch_size 8 \\
    --num_epochs 200 \\
    --gpus 0,1

## 4. 验证配置文件
python -c "
import sys
sys.path.append('src')
from lib.utils.config_loader import ConfigLoader
from lib.configs.dataset_configs import DATASET_PATH_CONFIGS

loader = ConfigLoader()
config = loader.load_config('src/lib/configs/dataset_configs.py', 'tableme_chinese_test')
print('配置加载成功:', config)
"
"""

# 自定义配置模板
CUSTOM_CONFIG_TEMPLATE: str = """
# 自定义TableLabelMe配置模板

## 创建自定义配置的步骤：

### 1. 复制dataset_configs.py文件
cp src/lib/configs/dataset_configs.py my_custom_configs.py

### 2. 在DATASET_PATH_CONFIGS中添加新配置
DATASET_PATH_CONFIGS["my_custom_config"] = {
    "description": "我的自定义TableLabelMe数据集配置",
    "train": [
        "/path/to/my/custom/train/data1",
        "/path/to/my/custom/train/data2"
    ],
    "val": [
        "/path/to/my/custom/val/data1",
        "/path/to/my/custom/val/data2"
    ],
    "metadata": {
        "total_sources": 2,
        "primary_language": "custom",
        "data_format": "TableLabelMe",
        "recommended_for": "custom_training"
    }
}

### 3. 使用自定义配置
python main.py ctdet_mid \\
    --dataset table \\
    --dataset_name TableLabelMe \\
    --data_config /absolute/path/to/my_custom_configs.py \\
    --config_name my_custom_config \\
    --exp_id my_custom_experiment

### 4. 配置验证要求
- 所有路径必须是绝对路径
- train和val字段是必需的
- 路径必须存在且可读
- 每个配置至少包含一个数据源
"""

# 常见问题和解决方案
FAQ_AND_SOLUTIONS: Dict[str, str] = {
    "配置文件路径错误": """
    问题：配置文件路径不存在或无法访问
    解决方案：
    1. 确保使用绝对路径
    2. 检查文件权限
    3. 验证路径格式（Windows使用反斜杠或正斜杠）
    """,

    "数据路径验证失败": """
    问题：配置中的数据路径验证失败
    解决方案：
    1. 确保所有数据路径都存在
    2. 检查路径权限（可读权限）
    3. 使用绝对路径而非相对路径
    4. 确认part_xxxx目录结构存在
    """,

    "配置名称不存在": """
    问题：指定的配置名称在配置文件中不存在
    解决方案：
    1. 检查配置名称拼写
    2. 查看可用配置：python -c "from lib.configs import DATASET_PATH_CONFIGS; print(list(DATASET_PATH_CONFIGS.keys()))"
    3. 使用默认配置：tableme_chinese_test
    """,

    "ConfigLoader导入失败": """
    问题：无法导入ConfigLoader模块
    解决方案：
    1. 确保在正确的目录下运行
    2. 检查Python路径：sys.path.append('src')
    3. 验证迭代4步骤1是否完成
    """
}

# 配置最佳实践
BEST_PRACTICES: Dict[str, str] = {
    "路径管理": "始终使用绝对路径，避免相对路径引起的问题",
    "配置命名": "使用描述性的配置名称，如tableme_chinese_production",
    "数据验证": "在训练前先验证配置和数据路径的有效性",
    "版本控制": "将自定义配置文件纳入版本控制系统",
    "文档记录": "为自定义配置添加详细的description和metadata",
    "测试优先": "使用小数据集配置（如tableme_chinese_test）进行初始测试",
    "环境隔离": "为不同环境（开发、测试、生产）使用不同的配置"
}
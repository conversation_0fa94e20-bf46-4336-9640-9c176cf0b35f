# src/lib/configs/__init__.py
"""
LORE-TSR配置文件包
提供TableLabelMe数据集的配置模板和示例

该包为LORE-TSR项目的TableLabelMe数据格式支持提供配置管理功能，
包含预定义的数据集路径配置、验证规则和使用示例。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

from .dataset_configs import DATASET_PATH_CONFIGS, CONFIG_VALIDATION_RULES
from .config_examples import EXAMPLE_USAGE, CUSTOM_CONFIG_TEMPLATE

__all__ = [
    'DATASET_PATH_CONFIGS',
    'CONFIG_VALIDATION_RULES',
    'EXAMPLE_USAGE',
    'CUSTOM_CONFIG_TEMPLATE'
]

__version__ = '1.0.0'
__author__ = 'LORE-TSR TableLabelMe Adaptation Team'
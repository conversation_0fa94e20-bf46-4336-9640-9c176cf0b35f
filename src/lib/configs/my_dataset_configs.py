# src/lib/configs/dataset_configs.py
"""
数据集配置文件模板 - TableLabelMe数据格式支持项目
定义多种预配置的数据集路径组合，支持多源数据配置、路径验证规则和配置示例

该模块为LORE-TSR项目提供TableLabelMe格式数据集的标准化配置管理，
包含预定义的数据集路径组合、验证规则和元数据信息。
"""

from typing import Dict, List, Any, Union
import os

# 主要配置字典 - 包含所有预定义的数据集路径配置
DATASET_PATH_CONFIGS: Dict[str, Dict[str, Union[List[str], str]]] = {
    "tableme_full": {
        "description": "完整的TableLabelMe数据集，包含所有数据源",
        "train": [
            # "/aipdf-mlp/lanx/workspace/projects/LORE-adapt/input_images/lore_test_data/train",
            # "/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/train",
            # "/aipdf-mlp/shared/tsr_training/wired_table/release/TALOCRTable/train",
            "/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_chinese/train",
            # "/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_english/train"
        ],
        "val": [
            # "/aipdf-mlp/lanx/workspace/projects/LORE-adapt/input_images/lore_test_data/val",
            # "/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/val",
            # "/aipdf-mlp/shared/tsr_training/wired_table/release/TALOCRTable/val",
            "/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_chinese/val",
            # "/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_english/val"
        ],
        "metadata": {
            "total_sources": 1,
            # "total_sources": 4,
            "primary_language": "mixed",
            "data_format": "TableLabelMe",
            "recommended_for": "production_training"
        }
    },

    "tableme_chinese_test": {
        "description": "中文TableLabelMe测试数据集（使用真实本地数据）",
        "train": [
            "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese"
        ],
        "val": [
            "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese"
        ],
        "metadata": {
            "total_sources": 1,
            "primary_language": "chinese",
            "data_format": "TableLabelMe",
            "recommended_for": "development_testing"
        }
    },

    "tableme_chinese_only": {
        "description": "仅中文TableLabelMe数据集",
        "train": [
            "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese/train"
        ],
        "val": [
            "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese/val"
        ],
        "metadata": {
            "total_sources": 1,
            "primary_language": "chinese",
            "data_format": "TableLabelMe",
            "recommended_for": "chinese_specific_training"
        }
    },

    "tableme_english_only": {
        "description": "仅英文TableLabelMe数据集",
        "train": ["/path/to/TabRecSet_english/train"],
        "val": ["/path/to/TabRecSet_english/val"],
        "metadata": {
            "total_sources": 1,
            "primary_language": "english",
            "data_format": "TableLabelMe",
            "recommended_for": "english_specific_training"
        }
    },

    "tableme_wtw_only": {
        "description": "仅WTW数据集",
        "train": ["/path/to/WTW/train"],
        "val": ["/path/to/WTW/val"],
        "metadata": {
            "total_sources": 1,
            "primary_language": "mixed",
            "data_format": "TableLabelMe",
            "recommended_for": "wtw_specific_training"
        }
    },

    "tableme_talocr_only": {
        "description": "仅TALOCRTable数据集",
        "train": ["/path/to/TALOCRTable/train"],
        "val": ["/path/to/TALOCRTable/val"],
        "metadata": {
            "total_sources": 1,
            "primary_language": "mixed",
            "data_format": "TableLabelMe",
            "recommended_for": "talocr_specific_training"
        }
    }
}

# 配置验证规则 - 定义配置数据的验证标准和约束
CONFIG_VALIDATION_RULES: Dict[str, Any] = {
    "required_keys": ["train", "val"],           # 必需的配置键
    "optional_keys": ["description", "metadata"], # 可选的配置键
    "path_validation": True,                     # 是否验证路径存在性
    "allow_empty_paths": False,                  # 是否允许空路径列表
    "require_absolute_paths": True,              # 是否要求绝对路径
    "max_paths_per_split": 10,                   # 每个分割最大路径数
    "min_paths_per_split": 1,                    # 每个分割最小路径数
    "supported_formats": ["TableLabelMe"],       # 支持的数据格式
    "path_existence_check": True,                # 是否检查路径存在性
    "permission_check": True                     # 是否检查路径权限
}

# 默认配置设置 - 提供系统默认值
DEFAULT_CONFIG_SETTINGS: Dict[str, Any] = {
    "default_config_name": "tableme_full",  # 默认使用的配置名称
    "fallback_config_name": "tableme_chinese_only", # 备用配置名称
    "config_file_encoding": "utf-8",                # 配置文件编码
    "path_separator": os.sep,                       # 路径分隔符
    "case_sensitive_paths": os.name != 'nt'         # 路径是否区分大小写
}

# 环境变量配置 - 支持通过环境变量覆盖配置
ENVIRONMENT_VARIABLE_MAPPINGS: Dict[str, str] = {
    "LORE_TSR_CONFIG_ROOT": "config_root_path",     # 配置文件根目录
    "LORE_TSR_DATA_ROOT": "data_root_path",         # 数据根目录
    "LORE_TSR_DEFAULT_CONFIG": "default_config_name" # 默认配置名称
}

# 配置模板元数据 - 提供配置的详细信息
CONFIG_METADATA: Dict[str, Any] = {
    "version": "1.0.0",
    "created_date": "2025-01-22",
    "last_modified": "2025-01-22",
    "author": "LORE-TSR TableLabelMe Adaptation Team",
    "description": "TableLabelMe数据格式支持的配置文件模板",
    "supported_datasets": [
        "TabRecSet_chinese",
        "TabRecSet_english",
        "WTW",
        "TALOCRTable"
    ],
    "configuration_count": len(DATASET_PATH_CONFIGS),
    "validation_rules_count": len(CONFIG_VALIDATION_RULES)
}

def get_available_configs() -> List[str]:
    """
    获取所有可用的配置名称列表

    Returns:
        List[str]: 可用配置名称列表
    """
    return list(DATASET_PATH_CONFIGS.keys())

def get_config_description(config_name: str) -> str:
    """
    获取指定配置的描述信息

    Args:
        config_name (str): 配置名称

    Returns:
        str: 配置描述，如果配置不存在则返回空字符串
    """
    config = DATASET_PATH_CONFIGS.get(config_name, {})
    return config.get("description", "")

def validate_config_name(config_name: str) -> bool:
    """
    验证配置名称是否有效

    Args:
        config_name (str): 要验证的配置名称

    Returns:
        bool: 配置名称是否有效
    """
    return config_name in DATASET_PATH_CONFIGS

def get_real_data_configs() -> List[str]:
    """
    获取包含真实数据路径的配置列表

    Returns:
        List[str]: 包含真实数据路径的配置名称列表
    """
    real_configs = []
    real_path_prefix = "D:/workspace/datasets/"

    for config_name, config_data in DATASET_PATH_CONFIGS.items():
        train_paths = config_data.get("train", [])
        val_paths = config_data.get("val", [])

        # 检查是否包含真实路径
        has_real_path = any(
            path.startswith(real_path_prefix)
            for path in train_paths + val_paths
        )

        if has_real_path:
            real_configs.append(config_name)

    return real_configs
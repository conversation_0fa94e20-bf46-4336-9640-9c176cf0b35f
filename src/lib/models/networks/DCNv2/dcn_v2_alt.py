import os
import torch
from torch.utils.cpp_extension import load

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")

# 查找源文件
main_file = [os.path.join(src_dir, f) for f in os.listdir(src_dir) if f.endswith('.cpp')]
cpu_sources = [os.path.join(src_dir, 'cpu', f) for f in os.listdir(os.path.join(src_dir, 'cpu')) if f.endswith('.cpp')]
cuda_sources = [os.path.join(src_dir, 'cuda', f) for f in os.listdir(os.path.join(src_dir, 'cuda')) if f.endswith('.cu')]

# 编译参数 - 分别传递给cxx和nvcc
cxx_args = []
nvcc_args = [
    '-DCUDA_HAS_FP16=1',
    '-D__CUDA_NO_HALF_OPERATORS__',
    '-D__CUDA_NO_HALF_CONVERSIONS__',
    '-D__CUDA_NO_HALF2_OPERATORS__',
]

# 动态编译扩展
_ext = load(
    name='_ext',
    sources=main_file + cpu_sources + cuda_sources,
    extra_include_paths=[src_dir],
    extra_cflags=cxx_args,
    extra_cuda_cflags=nvcc_args,
    verbose=True
)

# 从原始的 dcn_v2.py 导入必要的类和函数
from .dcn_v2 import DCN, DCNPooling, _DCNv2, _DCNv2Pooling, _get_reference_points

# 确保 _ext 模块可用
if not hasattr(_ext, 'dcn_v2_forward'):
    raise ImportError("Failed to load DCNv2 CUDA extension")

print("DCNv2 extension loaded successfully")
